<?xml version="1.0" encoding="UTF-8" ?>
<!-- generator="WordPress/6.1.1" created="2023-02-09 20:09" -->

<channel>
	<title>Boilerplate</title>
	<pubDate>Thu, 09 Feb 2023 20:09:30 +0000</pubDate>

	<form>
		<id>3</id>
		<form_key><![CDATA[job_form]]></form_key>
		<name><![CDATA[Formulaire d\'offre d\'emploi]]></name>
		<description><![CDATA[]]></description>
		<created_at>2023-02-06 20:54:11</created_at>
		<logged_in>0</logged_in>
		<is_template>0</is_template>
		<editable>0</editable>
		<options><![CDATA[{"success_url":"\/merci","show_form":"1","ajax_submit":"1","js_validate":"1","success_msg":"Votre candidature a bien \u00e9t\u00e9 soumis avec succ\u00e8s. Nous vous contacterons dans les plus brefs d\u00e9lais!","edit_msg":"Your submission was successfully saved.","logged_in_role":[""],"cookie_expiration":"8000","editable_role":[""],"edit_page_id":"","protect_files_role":[""],"open_date":"2019-05-08 14:15","submit_value":"Postuler","edit_value":"Mettre \u00e0 jour","submit_conditions":{"show_hide":"show","any_all":"all"},"start_over_label":"Start Over","before_html":"<legend class=\\\"frm_screen_reader\\\">[form_name]<\/legend>\r\n[if form_name]<h3 class=\\\"frm_form_title\\\">[form_name]<\/h3>[\/if form_name]\r\n[if form_description]<div class=\\\"frm_description\\\">[form_description]<\/div>[\/if form_description]","submit_html":"<div class=\\\"frm_submit\\\">\r\n[if back_button]<button type=\\\"submit\\\" name=\\\"frm_prev_page\\\" formnovalidate=\\\"formnovalidate\\\" class=\\\"frm_prev_page\\\" [back_hook]>[back_label]<\/button>[\/if back_button]\r\n<button class=\\\"frm_button_submit\\\" type=\\\"submit\\\"  [button_action]><span>[button_label]<\/span><span class=\\\"uk-icon\\\" data-uk-icon=\\\"chevron-right\\\"><\/span><\/button>\r\n[if save_draft]<a href=\\\"#\\\" tabindex=\\\"0\\\" class=\\\"frm_save_draft\\\" [draft_hook]>[draft_label]<\/a>[\/if save_draft]\r\n<\/div>"}]]></options>
		<status><![CDATA[published]]></status>
		<parent_form_id>0</parent_form_id>
		<field>
			<id>20</id>
			<field_key><![CDATA[qh4icy2]]></field_key>
			<name><![CDATA[Prénom et nom]]></name>
			<description><![CDATA[]]></description>
			<type><![CDATA[text]]></type>
			<default_value><![CDATA[]]></default_value>
			<field_order>2</field_order>
			<form_id>3</form_id>
			<required>1</required>
			<options><![CDATA[]]></options>
			<field_options><![CDATA[{"blank":"Ce champ ne peut \u00eatre vide.","invalid":"Name is invalid","default_blank":"0","classes":"frm12 frm_first","custom_html":"<div id=\\\"frm_field_[id]_container\\\" class=\\\"frm_form_field form-field [required_class][error_class]\\\">\r\n    <label for=\\\"field_[key]\\\" id=\\\"field_[key]_label\\\" class=\\\"frm_primary_label\\\">[field_name]\r\n        <span class=\\\"frm_required\\\">[required_label]<\/span>\r\n    <\/label>\r\n    [input]\r\n    [if description]<div class=\\\"frm_description\\\" id=\\\"frm_desc_field_[key]\\\">[description]<\/div>[\/if description]\r\n    [if error]<div class=\\\"frm_error\\\" id=\\\"frm_error_field_[key]\\\">[error]<\/div>[\/if error]\r\n<\/div>","in_section":"0","admin_only":[""],"unique_msg":"This value must be unique.","use_calc":0,"autopopulate_value":false,"watch_lookup":[""]}]]></field_options>
		</field>
		<field>
			<id>22</id>
			<field_key><![CDATA[29yf4d2]]></field_key>
			<name><![CDATA[Courriel]]></name>
			<description><![CDATA[]]></description>
			<type><![CDATA[email]]></type>
			<default_value><![CDATA[]]></default_value>
			<field_order>4</field_order>
			<form_id>3</form_id>
			<required>1</required>
			<options><![CDATA[]]></options>
			<field_options><![CDATA[{"blank":"Ce champ ne peut \u00eatre vide.","invalid":"S'il vous pla\u00eet, veuillez inscrire une adresse courriel valide","default_blank":"0","classes":"frm12 frm_first","custom_html":"<div id=\\\"frm_field_[id]_container\\\" class=\\\"frm_form_field form-field [required_class][error_class]\\\">\r\n    <label for=\\\"field_[key]\\\" id=\\\"field_[key]_label\\\" class=\\\"frm_primary_label\\\">[field_name]\r\n        <span class=\\\"frm_required\\\">[required_label]<\/span>\r\n    <\/label>\r\n    [input]\r\n    [if description]<div class=\\\"frm_description\\\" id=\\\"frm_desc_field_[key]\\\">[description]<\/div>[\/if description]\r\n    [if error]<div class=\\\"frm_error\\\" id=\\\"frm_error_field_[key]\\\">[error]<\/div>[\/if error]\r\n<\/div>","in_section":"0","admin_only":[""],"unique_msg":"This value must be unique.","use_calc":0,"autopopulate_value":false,"watch_lookup":[""]}]]></field_options>
		</field>
		<field>
			<id>26</id>
			<field_key><![CDATA[8qgqu]]></field_key>
			<name><![CDATA[Téléphone]]></name>
			<description><![CDATA[]]></description>
			<type><![CDATA[phone]]></type>
			<default_value><![CDATA[]]></default_value>
			<field_order>6</field_order>
			<form_id>3</form_id>
			<required>0</required>
			<options><![CDATA[]]></options>
			<field_options><![CDATA[{"admin_only":[""],"in_section":"0","blank":"","invalid":"S'il vous pla\u00eet, veuillez inscrire un num\u00e9ro de t\u00e9l\u00e9phone valide","classes":"frm12 frm_first","custom_html":"<div id=\\\"frm_field_[id]_container\\\" class=\\\"frm_form_field form-field [required_class][error_class]\\\">\r\n    <label for=\\\"field_[key]\\\" id=\\\"field_[key]_label\\\" class=\\\"frm_primary_label\\\">[field_name]\r\n        <span class=\\\"frm_required\\\" aria-hidden=\\\"true\\\">[required_label]<\/span>\r\n    <\/label>\r\n    [input]\r\n    [if description]<div class=\\\"frm_description\\\" id=\\\"frm_desc_field_[key]\\\">[description]<\/div>[\/if description]\r\n    [if error]<div class=\\\"frm_error\\\" role=\\\"alert\\\" id=\\\"frm_error_field_[key]\\\">[error]<\/div>[\/if error]\r\n<\/div>"}]]></field_options>
		</field>
		<field>
			<id>27</id>
			<field_key><![CDATA[posttype_job]]></field_key>
			<name><![CDATA[Poste désiré]]></name>
			<description><![CDATA[]]></description>
			<type><![CDATA[select]]></type>
			<default_value><![CDATA[]]></default_value>
			<field_order>8</field_order>
			<form_id>3</form_id>
			<required>1</required>
			<options><![CDATA[{"1":{"label":"Candidature spontan\u00e9e","value":"other"},"2":{"label":"Offre d\u2019emploi lorem ipsum","value":"2199"}}]]></options>
			<field_options><![CDATA[{"admin_only":[""],"dyn_default_value":"[post_id]","other":"0","in_section":"0","blank":"Veuillez choisir un poste d\u00e9sir\u00e9","separate_value":"1","classes":"frm12 frm_first","custom_html":"<div id=\\\"frm_field_[id]_container\\\" class=\\\"frm_form_field form-field [required_class][error_class]\\\">\r\n    <label for=\\\"field_[key]\\\" id=\\\"field_[key]_label\\\" class=\\\"frm_primary_label\\\">[field_name]\r\n        <span class=\\\"frm_required\\\" aria-hidden=\\\"true\\\">[required_label]<\/span>\r\n    <\/label>\r\n    [input]\r\n    [if description]<div class=\\\"frm_description\\\" id=\\\"frm_desc_field_[key]\\\">[description]<\/div>[\/if description]\r\n    [if error]<div class=\\\"frm_error\\\" role=\\\"alert\\\" id=\\\"frm_error_field_[key]\\\">[error]<\/div>[\/if error]\r\n<\/div>","placeholder":"Choisir un poste"}]]></field_options>
		</field>
		<field>
			<id>28</id>
			<field_key><![CDATA[lp4nu]]></field_key>
			<name><![CDATA[Spécifier le poste désiré]]></name>
			<description><![CDATA[]]></description>
			<type><![CDATA[text]]></type>
			<default_value><![CDATA[]]></default_value>
			<field_order>10</field_order>
			<form_id>3</form_id>
			<required>0</required>
			<options><![CDATA[]]></options>
			<field_options><![CDATA[{"hide_field":["27"],"hide_opt":["other"],"admin_only":[""],"in_section":"0","blank":"","invalid":"Text is invalid","custom_html":"<div id=\\\"frm_field_[id]_container\\\" class=\\\"frm_form_field form-field [required_class][error_class]\\\">\r\n    <label for=\\\"field_[key]\\\" id=\\\"field_[key]_label\\\" class=\\\"frm_primary_label\\\">[field_name]\r\n        <span class=\\\"frm_required\\\" aria-hidden=\\\"true\\\">[required_label]<\/span>\r\n    <\/label>\r\n    [input]\r\n    [if description]<div class=\\\"frm_description\\\" id=\\\"frm_desc_field_[key]\\\">[description]<\/div>[\/if description]\r\n    [if error]<div class=\\\"frm_error\\\" role=\\\"alert\\\" id=\\\"frm_error_field_[key]\\\">[error]<\/div>[\/if error]\r\n<\/div>"}]]></field_options>
		</field>
		<field>
			<id>24</id>
			<field_key><![CDATA[9jv0r12]]></field_key>
			<name><![CDATA[Message]]></name>
			<description><![CDATA[]]></description>
			<type><![CDATA[textarea]]></type>
			<default_value><![CDATA[]]></default_value>
			<field_order>12</field_order>
			<form_id>3</form_id>
			<required>1</required>
			<options><![CDATA[]]></options>
			<field_options><![CDATA[{"blank":"Ce champ ne peut \u00eatre vide.","invalid":"Votre message is invalid","default_blank":"0","classes":"frm_full","custom_html":"<div id=\\\"frm_field_[id]_container\\\" class=\\\"frm_form_field form-field [required_class][error_class]\\\">\r\n    <label for=\\\"field_[key]\\\" id=\\\"field_[key]_label\\\" class=\\\"frm_primary_label\\\">[field_name]\r\n        <span class=\\\"frm_required\\\">[required_label]<\/span>\r\n    <\/label>\r\n    [input]\r\n    [if description]<div class=\\\"frm_description\\\" id=\\\"frm_desc_field_[key]\\\">[description]<\/div>[\/if description]\r\n    [if error]<div class=\\\"frm_error\\\" id=\\\"frm_error_field_[key]\\\">[error]<\/div>[\/if error]\r\n<\/div>","in_section":"0","admin_only":[""],"unique_msg":"This value must be unique.","use_calc":0,"autopopulate_value":false,"watch_lookup":[""],"max_limit":"0"}]]></field_options>
		</field>
		<field>
			<id>25</id>
			<field_key><![CDATA[p9kn72]]></field_key>
			<name><![CDATA[CV]]></name>
			<description><![CDATA[]]></description>
			<type><![CDATA[file]]></type>
			<default_value><![CDATA[]]></default_value>
			<field_order>14</field_order>
			<form_id>3</form_id>
			<required>1</required>
			<options><![CDATA[]]></options>
			<field_options><![CDATA[{"admin_only":[""],"use_calc":0,"in_section":"0","size":"15","label":"none","blank":"Ce champ ne peut \u00eatre vide.","invalid":"File Upload is invalid","default_blank":0,"custom_html":"<div id=\\\"frm_field_[id]_container\\\" class=\\\"frm_form_field form-field [required_class][error_class]\\\">\r\n    <div  id=\\\"field_[key]_label\\\" class=\\\"frm_primary_label\\\">[field_name]\r\n        <span class=\\\"frm_required\\\">[required_label]<\/span>\r\n    <\/div>\r\n    [input]\r\n    [if description]<div class=\\\"frm_description\\\" id=\\\"frm_desc_field_[key]\\\">[description]<\/div>[\/if description]\r\n    [if error]<div class=\\\"frm_error\\\" id=\\\"frm_error_field_[key]\\\">[error]<\/div>[\/if error]\r\n<\/div>","attach":"1","delete":"1","restrict":"0","drop_msg":"D\u00e9poser un fichier ici ou cliquer pour t\u00e9l\u00e9charger","choose_msg":"Choisir le fichier"}]]></field_options>
		</field>
	</form>
	<view>
		<title>Admin Notification</title>
		<link></link>
		<post_author><![CDATA[voyou]]></post_author>
		<description></description>
		<content><![CDATA[{"event":["create"],"email_to":"<EMAIL>","cc":"","bcc":"","from":"[sitename] <[admin_email]>","reply_to":"[22]","email_subject":"[sitename] - Formulaire d'offre d'emploi","email_message":"[default-message]","email_attachment_id":"","conditions":{"send_stop":"send","any_all":"any"}}]]></content>
		<excerpt><![CDATA[email]]></excerpt>
		<post_id>2200</post_id>
		<post_date>2015-04-06 17:18:12</post_date>
		<post_date_gmt>2015-04-06 22:18:12</post_date_gmt>
		<comment_status>closed</comment_status>
		<ping_status>closed</ping_status>
		<post_name>3_email_2200</post_name>
		<status>publish</status>
		<post_parent>0</post_parent>
		<menu_order>3</menu_order>
		<post_type>frm_form_actions</post_type>
		<post_password><![CDATA[]]></post_password>
		<is_sticky>0</is_sticky>
		<postmeta>
			<meta_key>_wp_old_slug</meta_key>
			<meta_value><![CDATA[1_email_9-2]]></meta_value>
		</postmeta>
	</view>
</channel>
