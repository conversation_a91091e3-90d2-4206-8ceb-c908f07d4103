<?xml version="1.0" encoding="UTF-8" ?>
<!-- generator="WordPress/6.0" created="2022-07-12 15:54" -->

<channel>
	<title>Boilerplate</title>
	<pubDate><PERSON><PERSON>, 12 Jul 2022 15:54:19 +0000</pubDate>

	<form>
		<id>2</id>
		<form_key><![CDATA[formulairevoeuxdesympathie]]></form_key>
		<name><![CDATA[Formulaire voeux de sympathie]]></name>
		<description><![CDATA[]]></description>
		<created_at>2022-03-07 14:23:59</created_at>
		<logged_in>0</logged_in>
		<is_template>0</is_template>
		<editable>0</editable>
		<options><![CDATA[{"antispam":"1","js_validate":"1","success_msg":"Nous avons bien re\u00e7u votre message de sympathie.","logged_in_role":[""],"cookie_expiration":"8000","editable_role":[""],"edit_page_id":"","protect_files":"0","noindex_files":"0","open_date":"2022-03-07 09:23","submit_value":"Soumettre","submit_conditions":{"show_hide":"show","any_all":"all"},"before_html":"<legend class=\\\"frm_screen_reader\\\">[form_name]<\/legend>\r\n[if form_name]<h3 class=\\\"frm_form_title\\\">[form_name]<\/h3>[\/if form_name]\r\n[if form_description]<div class=\\\"frm_description\\\">[form_description]<\/div>[\/if form_description]","submit_html":"<div class=\\\"frm_submit\\\">\r\n[if back_button]<button type=\\\"submit\\\" name=\\\"frm_prev_page\\\" formnovalidate=\\\"formnovalidate\\\" class=\\\"frm_prev_page\\\" [back_hook]>[back_label]<\/button>[\/if back_button]\r\n<button class=\\\"frm_button_submit\\\" type=\\\"submit\\\"  [button_action]>[button_label]<span data-uk-icon=\\\"chevron-right\\\" class=\\\"uk-icon\\\"><svg width=\\\"10\\\" height=\\\"16\\\" viewBox=\\\"0 0 10 16\\\" fill=\\\"none\\\" xmlns=\\\"http:\/\/www.w3.org\/2000\/svg\\\"><path d=\\\"M1 1.25L7.75001 8.00001L1 14.75\\\" stroke=\\\"#363433\\\" stroke-width=\\\"2\\\"><\/path><\/svg><\/span><\/button>\r\n[if save_draft]<a href=\\\"#\\\" tabindex=\\\"0\\\" class=\\\"frm_save_draft\\\" [draft_hook]>[draft_label]<\/a>[\/if save_draft]\r\n<\/div>"}]]></options>
		<status><![CDATA[published]]></status>
		<parent_form_id>0</parent_form_id>
		<field>
			<id>12</id>
			<field_key><![CDATA[jhtmg]]></field_key>
			<name><![CDATA[Nom]]></name>
			<description><![CDATA[]]></description>
			<type><![CDATA[text]]></type>
			<default_value><![CDATA[]]></default_value>
			<field_order>2</field_order>
			<form_id>2</form_id>
			<required>1</required>
			<options><![CDATA[]]></options>
			<field_options><![CDATA[{"blank":"","invalid":"Text is invalid","classes":"frm12 frm_first","custom_html":"<div id=\\\"frm_field_[id]_container\\\" class=\\\"frm_form_field form-field [required_class][error_class]\\\">\r\n    <label for=\\\"field_[key]\\\" id=\\\"field_[key]_label\\\" class=\\\"frm_primary_label\\\">[field_name]\r\n        <span class=\\\"frm_required\\\" aria-hidden=\\\"true\\\">[required_label]<\/span>\r\n    <\/label>\r\n    [input]\r\n    [if description]<div class=\\\"frm_description\\\" id=\\\"frm_desc_field_[key]\\\">[description]<\/div>[\/if description]\r\n    [if error]<div class=\\\"frm_error\\\" id=\\\"frm_error_field_[key]\\\">[error]<\/div>[\/if error]\r\n<\/div>","post_field":"post_title","admin_only":[""],"in_section":"0"}]]></field_options>
		</field>
		<field>
			<id>13</id>
			<field_key><![CDATA[toaim]]></field_key>
			<name><![CDATA[Courriel]]></name>
			<description><![CDATA[]]></description>
			<type><![CDATA[email]]></type>
			<default_value><![CDATA[]]></default_value>
			<field_order>4</field_order>
			<form_id>2</form_id>
			<required>0</required>
			<options><![CDATA[]]></options>
			<field_options><![CDATA[{"blank":"Ce champ ne peut \u00eatre vide.","invalid":"Email is invalid","classes":"frm12 frm_first","custom_html":"<div id=\\\"frm_field_[id]_container\\\" class=\\\"frm_form_field form-field [required_class][error_class]\\\">\r\n    <label for=\\\"field_[key]\\\" id=\\\"field_[key]_label\\\" class=\\\"frm_primary_label\\\">[field_name]\r\n        <span class=\\\"frm_required\\\" aria-hidden=\\\"true\\\">[required_label]<\/span>\r\n    <\/label>\r\n    [input]\r\n    [if description]<div class=\\\"frm_description\\\" id=\\\"frm_desc_field_[key]\\\">[description]<\/div>[\/if description]\r\n    [if error]<div class=\\\"frm_error\\\" id=\\\"frm_error_field_[key]\\\">[error]<\/div>[\/if error]\r\n<\/div>","post_field":"post_custom","custom_field":"email","admin_only":[""],"in_section":"0"}]]></field_options>
		</field>
		<field>
			<id>14</id>
			<field_key><![CDATA[jg1ox]]></field_key>
			<name><![CDATA[Ville]]></name>
			<description><![CDATA[]]></description>
			<type><![CDATA[text]]></type>
			<default_value><![CDATA[]]></default_value>
			<field_order>6</field_order>
			<form_id>2</form_id>
			<required>1</required>
			<options><![CDATA[]]></options>
			<field_options><![CDATA[{"blank":"","invalid":"Text is invalid","classes":"frm12 frm_first","custom_html":"<div id=\\\"frm_field_[id]_container\\\" class=\\\"frm_form_field form-field [required_class][error_class]\\\">\r\n    <label for=\\\"field_[key]\\\" id=\\\"field_[key]_label\\\" class=\\\"frm_primary_label\\\">[field_name]\r\n        <span class=\\\"frm_required\\\" aria-hidden=\\\"true\\\">[required_label]<\/span>\r\n    <\/label>\r\n    [input]\r\n    [if description]<div class=\\\"frm_description\\\" id=\\\"frm_desc_field_[key]\\\">[description]<\/div>[\/if description]\r\n    [if error]<div class=\\\"frm_error\\\" id=\\\"frm_error_field_[key]\\\">[error]<\/div>[\/if error]\r\n<\/div>","post_field":"post_custom","custom_field":"ville","admin_only":[""],"in_section":"0"}]]></field_options>
		</field>
		<field>
			<id>15</id>
			<field_key><![CDATA[xgdy]]></field_key>
			<name><![CDATA[Message proposé]]></name>
			<description><![CDATA[]]></description>
			<type><![CDATA[select]]></type>
			<default_value><![CDATA[]]></default_value>
			<field_order>8</field_order>
			<form_id>2</form_id>
			<required>0</required>
			<options><![CDATA[[{"label":"","value":""}]]]></options>
			<field_options><![CDATA[{"blank":"","separate_value":"1","classes":"message_propose","custom_html":"<div id=\"frm_field_[id]_container\" class=\"frm_form_field form-field [required_class][error_class]\">\r\n    <label for=\"field_[key]\" id=\"field_[key]_label\" class=\"frm_primary_label\">[field_name]\r\n        <span class=\"frm_required\" aria-hidden=\"true\">[required_label]<\/span>\r\n    <\/label>\r\n    [input]\r\n    [if description]<div class=\"frm_description\" id=\"frm_desc_field_[key]\">[description]<\/div>[\/if description]\r\n    [if error]<div class=\"frm_error\" id=\"frm_error_field_[key]\">[error]<\/div>[\/if error]\r\n<\/div>","admin_only":[""],"other":"0","in_section":"0"}]]></field_options>
		</field>
		<field>
			<id>16</id>
			<field_key><![CDATA[225od]]></field_key>
			<name><![CDATA[Message de sympathie]]></name>
			<description><![CDATA[]]></description>
			<type><![CDATA[textarea]]></type>
			<default_value><![CDATA[]]></default_value>
			<field_order>10</field_order>
			<form_id>2</form_id>
			<required>1</required>
			<options><![CDATA[]]></options>
			<field_options><![CDATA[{"blank":"","classes":"message_sympathie","custom_html":"<div id=\\\"frm_field_[id]_container\\\" class=\\\"frm_form_field form-field [required_class][error_class]\\\">\r\n    <label for=\\\"field_[key]\\\" id=\\\"field_[key]_label\\\" class=\\\"frm_primary_label\\\">[field_name]\r\n        <span class=\\\"frm_required\\\" aria-hidden=\\\"true\\\">[required_label]<\/span>\r\n    <\/label>\r\n    [input]\r\n    [if description]<div class=\\\"frm_description\\\" id=\\\"frm_desc_field_[key]\\\">[description]<\/div>[\/if description]\r\n    [if error]<div class=\\\"frm_error\\\" id=\\\"frm_error_field_[key]\\\">[error]<\/div>[\/if error]\r\n<\/div>","post_field":"post_content","admin_only":[""],"in_section":"0","get_values_form":"7","get_values_field":"52"}]]></field_options>
		</field>
		<field>
			<id>17</id>
			<field_key><![CDATA[w2zyf]]></field_key>
			<name><![CDATA[reCAPTCHA]]></name>
			<description><![CDATA[]]></description>
			<type><![CDATA[captcha]]></type>
			<default_value><![CDATA[]]></default_value>
			<field_order>12</field_order>
			<form_id>2</form_id>
			<required>0</required>
			<options><![CDATA[]]></options>
			<field_options><![CDATA[{"blank":"","invalid":"The reCAPTCHA was not entered correctly","custom_html":"<div id=\\\"frm_field_[id]_container\\\" class=\\\"frm_form_field form-field [required_class][error_class]\\\">\r\n    <label for=\\\"field_[key]\\\" id=\\\"field_[key]_label\\\" class=\\\"frm_primary_label\\\">[field_name]\r\n        <span class=\\\"frm_required\\\" aria-hidden=\\\"true\\\">[required_label]<\/span>\r\n    <\/label>\r\n    [input]\r\n    [if description]<div class=\\\"frm_description\\\" id=\\\"frm_desc_field_[key]\\\">[description]<\/div>[\/if description]\r\n    [if error]<div class=\\\"frm_error\\\" role=\\\"alert\\\" id=\\\"frm_error_field_[key]\\\">[error]<\/div>[\/if error]\r\n<\/div>","admin_only":[""],"in_section":"0"}]]></field_options>
		</field>
		<field>
			<id>18</id>
			<field_key><![CDATA[c35rr]]></field_key>
			<name><![CDATA[Post ID]]></name>
			<description><![CDATA[]]></description>
			<type><![CDATA[hidden]]></type>
			<default_value><![CDATA[[post_id]]]></default_value>
			<field_order>14</field_order>
			<form_id>2</form_id>
			<required>0</required>
			<options><![CDATA[]]></options>
			<field_options><![CDATA[{"blank":"","post_field":"post_custom","custom_field":"orbitary_id","in_section":"0"}]]></field_options>
		</field>
		<field>
			<id>19</id>
			<field_key><![CDATA[r7xeg]]></field_key>
			<name><![CDATA[Post name]]></name>
			<description><![CDATA[]]></description>
			<type><![CDATA[hidden]]></type>
			<default_value><![CDATA[[post_title]]]></default_value>
			<field_order>16</field_order>
			<form_id>2</form_id>
			<required>0</required>
			<options><![CDATA[]]></options>
			<field_options><![CDATA[{"blank":"","post_field":"post_custom","custom_field":"orbitary_name","in_section":"0"}]]></field_options>
		</field>
	</form>
	<view>
		<title>Send Response</title>
		<link></link>
		<post_author><![CDATA[voyou]]></post_author>
		<description></description>
		<content><![CDATA[{"event":["create"],"email_to":"[13]","cc":"","bcc":"","from":"[sitename] <[admin_email]>","reply_to":"","email_subject":"","email_message":"[12]\r\n[16]\r\n[13]\r\n[14]","email_attachment_id":"","conditions":{"send_stop":"send","any_all":"any"}}]]></content>
		<excerpt><![CDATA[email]]></excerpt>
		<post_id>315</post_id>
		<post_date>2022-03-07 09:23:59</post_date>
		<post_date_gmt>2022-03-07 14:23:59</post_date_gmt>
		<comment_status>closed</comment_status>
		<ping_status>closed</ping_status>
		<post_name>2_email_315</post_name>
		<status>publish</status>
		<post_parent>0</post_parent>
		<menu_order>2</menu_order>
		<post_type>frm_form_actions</post_type>
		<post_password><![CDATA[]]></post_password>
		<is_sticky>0</is_sticky>
		<postmeta>
			<meta_key>_wp_old_slug</meta_key>
			<meta_value><![CDATA[7_email_14248]]></meta_value>
		</postmeta>
		<postmeta>
			<meta_key>_wp_old_slug</meta_key>
			<meta_value><![CDATA[7_email_15273]]></meta_value>
		</postmeta>
	</view>
	<view>
		<title>Send Email</title>
		<link></link>
		<post_author><![CDATA[voyou]]></post_author>
		<description></description>
		<content><![CDATA[{"event":["create"],"email_to":"[admin_email]","cc":"","bcc":"","from":"[sitename] <[admin_email]>","reply_to":"[13]","email_subject":"","email_message":"http:\/\/maison-marc-leclerc.voyou-web.com\/wp\/wp-admin\/edit.php?post_status=pending&post_type=voeux-de-sympathie\r\n[default-message]","email_attachment_id":"","conditions":{"send_stop":"send","any_all":"any"}}]]></content>
		<excerpt><![CDATA[email]]></excerpt>
		<post_id>317</post_id>
		<post_date>2022-03-21 15:21:31</post_date>
		<post_date_gmt>0000-00-00 00:00:00</post_date_gmt>
		<comment_status>closed</comment_status>
		<ping_status>closed</ping_status>
		<post_name>2_email_317</post_name>
		<status>draft</status>
		<post_parent>0</post_parent>
		<menu_order>2</menu_order>
		<post_type>frm_form_actions</post_type>
		<post_password><![CDATA[]]></post_password>
		<is_sticky>0</is_sticky>
		<postmeta>
			<meta_key>_wp_old_slug</meta_key>
			<meta_value><![CDATA[7_email_14369]]></meta_value>
		</postmeta>
	</view>
	<view>
		<title>Create Wishes</title>
		<link></link>
		<post_author><![CDATA[voyou]]></post_author>
		<description></description>
		<content><![CDATA[{"event":["create","update","import"],"post_type":"wishes","post_title":"12","post_content":"16","display_id":"","post_excerpt":"","post_password":"","post_name":"","post_date":"","post_status":"publish","post_parent":"","menu_order":"","post_custom_fields":{"email":{"meta_name":"email","field_id":"13"},"ville":{"meta_name":"ville","field_id":"14"},"orbitary_id":{"meta_name":"orbitary_id","field_id":"18"},"orbitary_name":{"meta_name":"orbitary_name","field_id":"19"}},"conditions":{"send_stop":"send","any_all":"any"}}]]></content>
		<excerpt><![CDATA[wppost]]></excerpt>
		<post_id>318</post_id>
		<post_date>2022-07-12 09:12:59</post_date>
		<post_date_gmt>2022-07-12 14:12:59</post_date_gmt>
		<comment_status>closed</comment_status>
		<ping_status>closed</ping_status>
		<post_name>2_wppost_416</post_name>
		<status>publish</status>
		<post_parent>0</post_parent>
		<menu_order>2</menu_order>
		<post_type>frm_form_actions</post_type>
		<post_password><![CDATA[]]></post_password>
		<is_sticky>0</is_sticky>
	</view>
</channel>
