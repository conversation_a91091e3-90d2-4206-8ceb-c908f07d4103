<?php

/**
 * Avsight WooCommerce Inventory Sync
 * 
 * Refactored modular version using proper MVC structure
 * 
 * @package AvsightSync
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Load environment variables and define Salesforce constants
if (!defined('AVSIGHT_SF_CONSUMER_KEY')) {
    define('AVSIGHT_SF_CONSUMER_KEY', getenv('AVSIGHT_SF_CONSUMER_KEY') ?: '');
}

if (!defined('AVSIGHT_SF_CONSUMER_SECRET')) {
    define('AVSIGHT_SF_CONSUMER_SECRET', getenv('AVSIGHT_SF_CONSUMER_SECRET') ?: '');
}

if (!defined('AVSIGHT_SF_USERNAME')) {
    define('AVSIGHT_SF_USERNAME', getenv('AVSIGHT_SF_USERNAME') ?: '');
}

if (!defined('AVSIGHT_SF_PASSWORD')) {
    define('AVSIGHT_SF_PASSWORD', getenv('AVSIGHT_SF_PASSWORD') ?: '');
}

if (!defined('AVSIGHT_SF_SECURITY_TOKEN')) {
    define('AVSIGHT_SF_SECURITY_TOKEN', getenv('AVSIGHT_SF_SECURITY_TOKEN') ?: '');
}

if (!defined('AVSIGHT_SF_API_VERSION')) {
    define('AVSIGHT_SF_API_VERSION', getenv('AVSIGHT_SF_API_VERSION') ?: 'v55.0');
}

// Determine Salesforce login URL based on environment
if (!defined('AVSIGHT_SF_LOGIN_URL')) {
    $avsight_sf_login_url = 'https://login.salesforce.com/services/oauth2/token'; // Default to Production

    if ((getenv('WP_ENV') === 'development') || (getenv('WP_HOME') !== 'https://www.optima-aero.ca')) {
        // $avsight_sf_login_url = 'https://test.salesforce.com/services/oauth2/token'; // Sandbox for development or non-production URL
        $avsight_sf_login_url = 'https://prod-avs-optimaaero--oasb.sandbox.my.salesforce.com/services/oauth2/token'; // Sandbox for development or non-production URL
    }
    define('AVSIGHT_SF_LOGIN_URL', $avsight_sf_login_url);
}

// Define constants
if (!defined('AVSIGHT_SYNC_BATCH_SIZE')) {
    define('AVSIGHT_SYNC_BATCH_SIZE', 50);
}

if (!defined('AVSIGHT_SYNC_BATCH_INTERVAL')) {
    define('AVSIGHT_SYNC_BATCH_INTERVAL', 30); // seconds
}

if (!defined('AVSIGHT_BULK_DELETE_BATCH_SIZE')) {
    define('AVSIGHT_BULK_DELETE_BATCH_SIZE', 50);
}

// Transient keys
define('AVSIGHT_SYNC_TRANSIENT_KEY', 'avsight_sync_status');
define('AVSIGHT_SYNC_TOTAL_ITEMS_KEY', 'avsight_sync_total_items');
define('AVSIGHT_SYNC_PROCESSED_ITEMS_KEY', 'avsight_sync_processed_items');
define('AVSIGHT_SYNC_LAST_RUN_START_KEY', 'avsight_sync_last_run_start');
define('AVSIGHT_SYNC_LAST_RUN_END_KEY', 'avsight_sync_last_run_end');
define('AVSIGHT_SYNC_DATA_FILE_PATH_KEY', 'avsight_sync_data_file_path');
define('AVSIGHT_SYNC_DATA_FILE_OFFSET_KEY', 'avsight_sync_data_file_offset');
define('AVSIGHT_ALL_PRODUCT_IDS_FILE_PATH_KEY', 'avsight_all_product_ids_file_path');
define('AVSIGHT_SYNCED_PRODUCT_IDS_FILE_PATH_KEY', 'avsight_synced_product_ids_file_path');
define('AVSIGHT_UNSYNCED_IDS_FILE_PATH_KEY', 'avsight_unsynced_ids_file_path');
define('AVSIGHT_UNSYNCED_IDS_FILE_OFFSET_KEY', 'avsight_unsynced_ids_file_offset');

// Bulk delete transient keys
define('AVSIGHT_BULK_DELETE_TOTAL_ITEMS_KEY', 'avsight_bulk_delete_total_items');
define('AVSIGHT_BULK_DELETE_PROCESSED_ITEMS_KEY', 'avsight_bulk_delete_processed_items');
define('AVSIGHT_BULK_DELETE_START_TIME_KEY', 'avsight_bulk_delete_start_time');
define('AVSIGHT_BULK_DELETE_END_TIME_KEY', 'avsight_bulk_delete_end_time');
define('AVSIGHT_BULK_DELETE_IDS_FILE_PATH_KEY', 'avsight_bulk_delete_ids_file_path');
define('AVSIGHT_BULK_DELETE_IDS_FILE_OFFSET_KEY', 'avsight_bulk_delete_ids_file_offset');

// Salesforce API URLs - dynamically set based on environment
if (!defined('AVSIGHT_SF_BULK_QUERY_URL')) {
    $base_url = str_replace('/services/oauth2/token', '', AVSIGHT_SF_LOGIN_URL);
    $bulk_query_url = $base_url . '/services/data/' . AVSIGHT_SF_API_VERSION . '/jobs/query';
    define('AVSIGHT_SF_BULK_QUERY_URL', $bulk_query_url);
    error_log('Avsight Sync Debug: Constructed bulk query URL: ' . $bulk_query_url);
}

if (!defined('AVSIGHT_SF_BULK_RESULTS_URL')) {
    $base_url = str_replace('/services/oauth2/token', '', AVSIGHT_SF_LOGIN_URL);
    $bulk_results_url = $base_url . '/services/data/' . AVSIGHT_SF_API_VERSION . '/jobs/query/{jobId}/results';
    define('AVSIGHT_SF_BULK_RESULTS_URL', $bulk_results_url);
    error_log('Avsight Sync Debug: Constructed bulk results URL: ' . $bulk_results_url);
}

// Autoload classes
spl_autoload_register(function ($class) {
    // Only autoload our namespace
    if (strpos($class, 'App\\AvsightSync\\') !== 0) {
        return;
    }

    // Convert namespace to file path
    $class_file = str_replace('App\\AvsightSync\\', '', $class);
    $file_path = get_template_directory() . '/app/AvsightSync/' . $class_file . '.php';

    if (file_exists($file_path)) {
        require_once $file_path;
    }
});

// Import classes
use App\AvsightSync\AvsightSync;
use App\AvsightSync\AvsightApi;
use App\AvsightSync\AvsightSyncOperations;
use App\AvsightSync\AvsightDeleteOperations;
use App\AvsightSync\AvsightAdmin;
use App\AvsightSync\AvsightAjax;
use App\AvsightSync\AvsightScheduler;

// Initialize the system
add_action('init', function () {
    // Only initialize if WooCommerce is active
    if (!class_exists('WooCommerce')) {
        add_action('admin_notices', function () {
            echo '<div class="notice notice-error"><p><strong>Avsight Sync:</strong> WooCommerce is required but not active.</p></div>';
        });
        return;
    }

    // Check if Salesforce configuration exists
    if (!defined('AVSIGHT_SF_CONSUMER_KEY') || !defined('AVSIGHT_SF_CONSUMER_SECRET')) {
        add_action('admin_notices', function () {
            echo '<div class="notice notice-warning"><p><strong>Avsight Sync:</strong> Salesforce API credentials are not configured. Please check your .env file.</p></div>';
        });
    }

    // Initialize the main controller
    AvsightSync::init();
});

// Legacy function wrappers for backward compatibility
if (!function_exists('avsight_run_inventory_sync')) {
    function avsight_run_inventory_sync()
    {
        AvsightSyncOperations::runInventorySync();
    }
}

if (!function_exists('avsight_get_salesforce_access_token')) {
    function avsight_get_salesforce_access_token()
    {
        return AvsightApi::getAccessToken();
    }
}

if (!function_exists('avsight_validate_salesforce_config')) {
    function avsight_validate_salesforce_config()
    {
        return AvsightApi::validateConfig();
    }
}

if (!function_exists('avsight_bulk_delete_products_init')) {
    function avsight_bulk_delete_products_init()
    {
        return AvsightDeleteOperations::initBulkDelete();
    }
}

// Register activation/deactivation hooks
register_activation_hook(__FILE__, function () {
    // Create upload directory for temp files
    $upload_dir = wp_upload_dir();
    $temp_dir = $upload_dir['basedir'] . '/avsight-sync-temp/';

    if (!file_exists($temp_dir)) {
        wp_mkdir_p($temp_dir);
    }

    // Add .htaccess to protect temp files
    $htaccess_content = "Order deny,allow\nDeny from all\n";
    file_put_contents($temp_dir . '.htaccess', $htaccess_content);
});

register_deactivation_hook(__FILE__, function () {
    // Clear all scheduled events
    wp_clear_scheduled_hook('avsight_process_inventory_batch_event');

    // Clean up transients
    $transients_to_clear = [
        AVSIGHT_SYNC_TRANSIENT_KEY,
        AVSIGHT_SYNC_TOTAL_ITEMS_KEY,
        AVSIGHT_SYNC_PROCESSED_ITEMS_KEY,
        AVSIGHT_SYNC_LAST_RUN_START_KEY,
        AVSIGHT_SYNC_LAST_RUN_END_KEY,
        AVSIGHT_SYNC_DATA_FILE_PATH_KEY,
        AVSIGHT_SYNC_DATA_FILE_OFFSET_KEY,
        AVSIGHT_ALL_PRODUCT_IDS_FILE_PATH_KEY,
        AVSIGHT_SYNCED_PRODUCT_IDS_FILE_PATH_KEY,
        AVSIGHT_UNSYNCED_IDS_FILE_PATH_KEY,
        AVSIGHT_UNSYNCED_IDS_FILE_OFFSET_KEY,
        AVSIGHT_BULK_DELETE_TOTAL_ITEMS_KEY,
        AVSIGHT_BULK_DELETE_PROCESSED_ITEMS_KEY,
        AVSIGHT_BULK_DELETE_START_TIME_KEY,
        AVSIGHT_BULK_DELETE_END_TIME_KEY,
        AVSIGHT_BULK_DELETE_IDS_FILE_PATH_KEY,
        AVSIGHT_BULK_DELETE_IDS_FILE_OFFSET_KEY,
        'avsight_sync_error',
        'avsight_delete_error',
    ];

    foreach ($transients_to_clear as $transient) {
        delete_transient($transient);
    }

    // Clean up temporary files
    $upload_dir = wp_upload_dir();
    $temp_dir = $upload_dir['basedir'] . '/avsight-sync-temp/';

    if (file_exists($temp_dir)) {
        $files = glob($temp_dir . '*');
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
    }
});


// Debug: Log environment variable loading
error_log('Avsight Sync: Environment check - WP_ENV: ' . (getenv('WP_ENV') ?: 'not set'));
error_log('Avsight Sync: Environment check - WP_HOME: ' . (getenv('WP_HOME') ?: 'not set'));
error_log('Avsight Sync: Login URL set to: ' . (defined('AVSIGHT_SF_LOGIN_URL') ? AVSIGHT_SF_LOGIN_URL : 'not defined'));
error_log('Avsight Sync: Consumer Key: ' . (defined('AVSIGHT_SF_CONSUMER_KEY') && !empty(AVSIGHT_SF_CONSUMER_KEY) ? 'configured' : 'missing'));
error_log('Avsight Sync: Username: ' . (defined('AVSIGHT_SF_USERNAME') && !empty(AVSIGHT_SF_USERNAME) ? 'configured' : 'missing'));
error_log('Avsight Sync: API Version: ' . (defined('AVSIGHT_SF_API_VERSION') ? AVSIGHT_SF_API_VERSION : 'not defined'));
error_log('Avsight Sync: Bulk Query URL: ' . (defined('AVSIGHT_SF_BULK_QUERY_URL') ? AVSIGHT_SF_BULK_QUERY_URL : 'not defined'));

// Log successful initialization
error_log('Avsight Sync: Modular system initialized successfully.');
