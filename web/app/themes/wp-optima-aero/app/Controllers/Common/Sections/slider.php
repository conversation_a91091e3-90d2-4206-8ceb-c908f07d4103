<?php
$class_wrapper = "";
$class_item = "uk-width-5-6";

$class_wrapper = "uk-position-relative uk-slider";
$class_items = "uk-slider-items uk-grid uk-grid-collapse uk-grid-match";
$grid_divider = get_sub_field('grid_divider');
if($grid_divider) $class_wrapper .= ' uk-grid-divider';

$class_image = "vy_slider_image";
$height = get_sub_field('height');
$height_viewport = get_sub_field('height');
if($height){
  $class_items .= " uk-height-".$height;
  $class_image .= " uk-cover-container";
}else{
  $class_image .= " uk-text-center";
}

// $content_class_content = " uk-flex uk-flex-middle";
$slider_data = "";
$slider_gallery = get_sub_field('gallery');
$slider_nav_dot = get_sub_field('nav_dot');
$slider_nav_arrow = get_sub_field('nav_arrow');
$slider_autoplay_enable = get_sub_field('autoplay_enable');
$slider_autoplay_interval = get_sub_field('autoplay_interval');
$slider_item_width = get_sub_field('width');
if ($slider_item_width) {
  $class_item = "";
  foreach($slider_item_width as $width){
    if($width != 'inherited'){
      $class_item .= ' uk-width-'.$width;
    }
  }
}
if(!$slider_item_width) $slider_data .= "center:true;";
if($slider_item_width) $slider_data .= "sets:true;";
if($slider_autoplay_enable) $slider_data .= "autoplay:true;";
if($slider_autoplay_interval) $slider_data .= "autoplay-interval:".$slider_autoplay_interval.";";

$maxsize = \App\get_grid_size($slider_item_width);
if(count($slider_gallery) <= $maxsize) $slider_data .= "draggable:false;";

$config['class_wrapper'] = $class_wrapper;
$config['height'] = $height;
$config['class_item'] = $class_item;
$config['class_items'] = $class_items;
$config['class_image'] = $class_image;

$config['slider_data'] = $slider_data;
$config['nav_dot'] = $slider_nav_dot;
$config['nav_arrow'] = $slider_nav_arrow;
$config['item_width'] = $slider_nav_arrow;
$config['slider_gallery'] = $slider_gallery;
?>
