<?php
$title = get_sub_field('title');
$title_default = get_the_title();
$description = get_sub_field('description');
$height = get_sub_field('height');
// $bg_color = get_sub_field('bg_color');
$tag_title = 'h1';
// if($bg_color) $bg_color = 'uk-background-'.$bg_color;
// if(!$bg_color) $bg_color = 'uk-section-secondary';
$light_enable = get_sub_field('light_enable');
$image = get_sub_field('bg_image');
$video = get_sub_field('video');



if(is_tax() && !$title){
    $current_term = get_term_by( 'slug', get_query_var( 'term' ), get_query_var( 'taxonomy' ) );
    $title = $current_term->name;
    $thumbnail_id = get_term_meta( $current_term->term_id, 'thumbnail_id', true );
    $image = acf_get_attachment($thumbnail_id);
    if(get_query_var( 'taxonomy' ) == 'product_cat'){
        $image = get_field('bg_image', $current_term);
    }
}elseif(is_404() &&  !$title){
    $title = App\get_string('404_error',__( 'Erreur 404!', TEXTDOMAIN));
}

if ( class_exists('WooCommerce') ){
    if(is_shop()) $title_default = get_the_title(get_option( 'woocommerce_shop_page_id' ));
    if(is_product()) $title = '';
    // if(isset($_GET['s']) && !empty($_GET['s'])) $title = __('Search','woocommerce');
    if(is_account_page() && !is_user_logged_in()) $title = __('Connexion / S\'enregistrer',TEXTDOMAIN);
}

if(is_singular('expertise')){
    $title = (get_field('title')) ? get_field('title') : get_the_title();
    $title_default = \App\get_string('expertise_single_ontitle',__('Expertise',TEXTDOMAIN));
    $image = acf_get_attachment(get_post_thumbnail_id(get_the_id()));
    $description = get_sub_field('description');
}
if(!$title) $title = $title_default;
if(!$description && has_excerpt()) $description = get_the_excerpt();

if(!is_front_page() && is_home()) {
    $title_default = get_the_title( get_option('page_for_posts', true) );
    if(!$title) $title = get_the_title( get_option('page_for_posts', true) );
    $description = get_the_excerpt( get_option('page_for_posts', true) );
}
if(is_singular('post')) {
    $title_default = false;
    if(!$title) $title = get_the_title();
    $description = false;
    $image = acf_get_attachment(get_post_thumbnail_id(get_the_id()));
}
if(is_singular('branch')) {
    $title_default = false;
    if(!$title) $title = get_the_title();
    $description = '';
    if($map = get_field('map')){
        if(get_field('address')){

            if(isset($map['address']) && $map['address']){
                $description .= '<a target="_blank" href="https://maps.google.com/?daddr='. $map['lat'] .','. get_field('map')['lng'].'" class="uk-link-text">';
            }
                $description .=  get_field('address');
            if(isset(get_field('map')['address']) && get_field('map')['address']){
                $description .= '</a>';
            }
        }
    }
    $image = acf_get_attachment(get_post_thumbnail_id(get_the_id()));
}

$class_banner = 'vy_banner_std uk-section uk-section-large uk-padding-remove-top uk-background-cover';
$class_countainer = 'uk-container vy_banner_std_container';
$class_content = 'vy_banner_std_content uk-panel';
$class_title = 'vy_banner_std_title';
$class_subtitle = 'vy_banner_std_subtitle';
$class_description = 'vy_banner_std_description uk-text-lead-small uk-panel uk-width-2-3@m';
$style_banner = '';

if ($image) $style_banner .= 'background-image:url('.$image['url'].')';
if($light_enable) $class_banner .= ' uk-light';
if(empty($style_banner)) $class_banner .= ' no-image';
// $class_banner .= ' '.$bg_color;

if($height){
  $class_banner .= ' uk-height-'.$height;
}


$config['title_default'] = $title_default;
$config['title'] = $title;
$config['tag_title'] = $tag_title;
$config['description'] = $description;
$config['image'] = $image;
$config['video'] = $video;

$config['class_banner'] = $class_banner;
$config['class_countainer'] = $class_countainer;
$config['class_title'] = $class_title;
$config['class_subtitle'] = $class_subtitle;
$config['class_description'] = $class_description;
$config['class_content'] = $class_content;

$config['style_banner'] = $style_banner;
// $config['bg_image'] = $bg_image;
?>
