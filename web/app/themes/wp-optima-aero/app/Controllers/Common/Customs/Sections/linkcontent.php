<?php

	use App\Controllers\Partials\Content;


	$class_wrapper = "";
	$content_align_options = get_field('global','options');
	$content_align = get_sub_field('content_align');
	if(!$content_align) $content_align = $content_align_options['content_align'];
	$content_align_mobile = get_sub_field('content_align_mobile');
	if(!$content_align_mobile) $content_align_mobile = $content_align_options['content_align_mobile'];

	$config['button'] = get_sub_field('button');
	$button_global_text = get_sub_field('button_global_text');
	$button_global_style = get_sub_field('button_global_style');

	$class_wrapper .= ' uk-text-'.$content_align_mobile.' uk-text-'.$content_align;

	$config['class_wrapper'] = $class_wrapper;

	$items = [];
	$custom_query_value = get_sub_field('custom_query');

	$post_type = $custom_query_value['post_type'];
	$posts_per_page = (($custom_query_value['posts_per_page'] == '-1')?((get_field('base_pagers','options'))?get_field('base_pagers','options'):get_option( 'posts_per_page' )):$custom_query_value['posts_per_page']) ;
	$orderby = $custom_query_value['orderby'];
	$order = $custom_query_value['order'];

	if($post_type != "manual"){
		$args = array(
			'post_type' => $post_type,
			'orderby' => $orderby,
			'order' => $order ,
			'posts_per_page' => $posts_per_page,
			'post__not_in' => array(get_the_ID()),
		);
	}else{
		$args = array(
			'post_type' => 'any',
			'posts_per_page' => $posts_per_page,
			'orderby' => 'post__in',
			'post__in' => $custom_query_value['manuel_select']
		);
	}
	if($post_type == "page" && !empty($custom_query_value['post_type_page_parent'])) $args['post_parent'] = $custom_query_value['post_type_page_parent'];
	elseif($post_type == "page" ) $args['post_parent'] = get_the_ID();

	$the_query = new \WP_Query($args);
    // pre_print_r($the_query);


	$items_count = 0;

	$config['title']= self::title();

	while ( $the_query->have_posts() ) : $the_query->the_post();

	$layouts_content = get_sub_field('layouts');


	$config['class_section'] .= ' vy_'.$layouts_content;
	$config['class_prefix'] = 'vy_'.$layouts_content;


	$item_ID = get_the_ID();
	$item_title = get_the_title();
	// $item_description = get_the_excerpt();
	$item_description = ((has_excerpt())? get_the_excerpt():((!empty(get_the_content()))?wp_trim_words(apply_filters('the_excerpt', get_the_content()),40):''));
	$item_button = '';
	$item_image_id = get_post_thumbnail_id();
	$item_image_url = get_the_post_thumbnail_url();
	$item_image_alt = get_the_post_thumbnail_caption();
	$item_link = get_permalink();

	$config['items_count'] = $items_count;
	$config['image_position'] = $custom_query_value['image_position'];
	$config = App::build_block($config, $layouts_content);

	if($layouts_content == 'grid' || $layouts_content == 'slider_grid'){
		$config['link_cover_enable'] = get_sub_field('link_cover_enable');
		$config['button_global_enable'] = get_sub_field('button_global_enable');
	}else{
		$config['link_cover_enable'] = false;
		$config['button_global_enable'] = true;
	}
	$config['content_align_mobile'] = get_sub_field('content_align_mobile');
	$config['remove_links'] = get_sub_field('remove_links');
	$items[$items_count] = array(
		'id' => $item_ID ?? '',
		'post_type' => $post_type ?? '',
		'title' => $item_title ?? '',
		// 'description' => $item_description ?? '',
		'text' => $item_description ?? '',
		'link_internal' => $item_link,
	);

    if((get_sub_field('button_global_enable') && !$config['remove_links'])){
        $items[$items_count]['button'] = array(
            'btn_style'=>$button_global_style,
            'btn_type'=>'internal',
            'btn_link_internal'=>$item_link,
            'btn_text'=>$button_global_text,
            'prepend_icon'=>0,
            'append_icon'=>'chevron-right',
        );
    }
	// $config['title'] = $item_title;
	// $config['text'] = $item_description;
	// $config['button'] =  array(
	//   'btn_style'=>'secondary',
	//   'btn_type'=>'internal',
	//   'btn_link_internal'=>$item_link,
	//   'btn_text'=>'',
	//   'prepend_icon'=>0,
	//   'append_icon'=>'chevron-right',
	// );

	if(isset($item_image_url) && !empty($item_image_url)){
		$items[$items_count]['image'] =$image = acf_get_attachment($item_image_id);
	//   $items[$items_count]['image']['url'] = $item_image_url;
	//   $items[$items_count]['image']['alt'] = $item_image_alt;
		// $config['image'] = $item_image_url

	}
	//$config['items'] = $items;




	if($layouts_content == 'slider_grid' || $layouts_content == 'slider_card'){
		// $config = array_merge(App::build_block($config, $type="grid"),App::build_block($config, $type="slider"));
		$config = App::build_block($config, $type="grid");
		$class_wrapper = "";
		$class_item = "uk-width-5-6";

		$class_wrapper = "uk-position-relative uk-slider";
		$class_items = "uk-slider-items uk-grid uk-grid-small uk-grid-match";
		$class_image = "vy_slider_image";
		$height = get_sub_field('height');
		$height_viewport = get_sub_field('height');
		if($height){
            $class_items .= " uk-height-".$height;
            $class_image .= " uk-cover-container";
		}else{
		    $class_image .= " uk-text-center";
		}

		// $content_class_content = " uk-flex uk-flex-middle";
		$slider_data = "";
		// $items = get_sub_field('items');
		// var_dump($item_title);
		// die();
		$slider_nav_dot = get_sub_field('nav_dot');
		$slider_nav_arrow = get_sub_field('nav_arrow');
		$slider_autoplay_enable = get_sub_field('autoplay_enable');
		$slider_autoplay_interval = get_sub_field('autoplay_interval');
		$slider_item_width = get_sub_field('width');

		// var_dump($slider_item_width);
		// die();
		if ($slider_item_width) {
		$class_item = "";
		foreach($slider_item_width as $width){
			if($width != 'inherited'){
			$class_item .= ' uk-width-'.$width;
			}
		}
		}
		if(!$slider_item_width) $slider_data .= "center:true;";
		if($slider_item_width) $slider_data .= "sets:true;";
		if($slider_autoplay_enable) $slider_data .= "autoplay:true;";
		if($slider_autoplay_interval) $slider_data .= "autoplay-interval:".$slider_autoplay_interval.";";

		// var_dump($slider_data);
		// die();


		$maxsize = \App\get_grid_size($slider_item_width);
		if(count($items) <= $maxsize) $slider_data .= "draggable:false;";

		$class_prefix = 'vy_grid';//.$type;

		$args_title = [
			'class' => $class_prefix .'_title'
		];

		$config['class_prefix'] = $class_prefix;

		$config['args_title'] = $args_title;
		$config['title']= self::title();
		$config['button'] = get_sub_field('button');


		$config['class_wrapper'] = $class_wrapper;
		$config['height'] = $height;
		$config['class_item'] = $class_item;
		$config['class_items'] = $class_items;
		$config['class_image'] = $class_image;

		$config['slider_data'] = $slider_data;
		$config['nav_dot'] = $slider_nav_dot;
		$config['nav_arrow'] = $slider_nav_arrow;
		$config['item_width'] = $slider_nav_arrow;
		$config['item_width'] = $slider_nav_arrow;
		$config['items'] = $items;
		// pre_print_r($config);
		$config['config'] = $config;
		// $return .= App::custom_include('sections.'.$layouts_content, $config);
	}elseif($layouts_content == 'standard'){
	// if($layouts_content == 'standard'){

		$items = $items[$items_count];
		$config['items'] = $items;
		if(isset($item_image_url) && !empty($item_image_url)){
			$config['image'] = self::image($config);

			unset($config['items']['image']);
		}

		$config['config'] = $config;

		// pre_print_r($config);
		// $return .= \App\template('sections.'.$layouts_content, $config);
		$return .= App::custom_include('sections.'.$layouts_content, $config);
	}elseif($layouts_content == 'cta'){

		$class_wrapper = "uk-flex-middle";

        $class_wrapper = ' '.App\get_class_grid();

		$config['class_wrapper'] = $class_wrapper;

		$items = $items[$items_count];
		$config['items'] = $items;
		if(isset($item_image_url) && !empty($item_image_url)){
		$config['images'] = self::image($config);

		unset($config['items']['image']);
		}

		$config['config'] = $config;

		// pre_print_r($config);
		// $return .= \App\template('sections.'.$layouts_content, $config);
		$return .= App::custom_include('sections.'.$layouts_content, $config);
	}elseif($layouts_content == 'gridstack'){
		$class_wrapper = "uk-grid uk-grid-collapse uk-child-width-1-2@m";
		$content_class_content = " uk-flex uk-flex-middle uk-flex-center vy_gridstack_item";

		$config['class_wrapper'] = $class_wrapper;
		$config['content_class_content'] = $config['content_class_content'].$content_class_content;
		// $config['class_section'] = 'vy_gridstack';
		$config['has_container'] = false;

		$items = $items[$items_count];
		if(empty($image_side_choice)) $image_side_choice = $config['image_position'];
		if( $image_side_choice == "oddeven") $image_side = ((isset($items_count) && $items_count % 2 == 0)?'':'uk-flex-last@m');
		elseif( $image_side_choice == "right") $image_side = 'uk-flex-last@m';
		else $image_side = '';
		/*$items_number = $items_count + 1;
		if ($items_number % 3 == 1) {
		$light_enable = 1;
		$bg_color = 'primary';
		}
		if ($items_number % 3 == 2) {
		$light_enable = 1;
		$bg_color = 'secondary';
		$items['button']['btn_style'] = 'link';
		}
		if ($items_number % 3 == 0) {
		$light_enable = 0;
		$bg_color = 'muted';
		$items['button']['btn_style'] = 'link';
		}*/

		$config['items'] = array(
		0 => array(
			'title' => '',
			'text' => '',
			'button' => '',
			'image_enable' => 1,
			'image_side_choice' => $image_side,
			'gridstack_content' => array (
				'bg_color' => $bg_color ?? '',
				'light_enable' => $light_enable,
				'content_align' => get_sub_field('content_align'),
				'content_align_mobile' => get_sub_field('content_align_mobile'),
				'content' => ''
			),
			'bg_image' => array()
			),
		1 => array(
			'title' => $items['title'],
			'text' => $items['text'] ?? '',
			'button' => $items['button'] ?? '',
			'image_enable' => 0,
			'gridstack_content' => array  (
				'bg_color' => $bg_color ?? '',
				'light_enable' => $light_enable,
				'content_align' => get_sub_field('content_align'),
				'content_align_mobile' => get_sub_field('content_align_mobile'),
				'content' => '',
			),
			'bg_image' => array()
		)
		);
		if(isset($item_image_url) && !empty($item_image_url)){
		$config['items'][0]['bg_image']['url'] = $item_image_url;
		$config['items'][0]['bg_image']['alt'] = $item_image_alt;
		}
		$config['image'] = self::image($config);
		$config['config'] = $config;
		// $return .= \App\template('sections.'.$layouts_content, $config);
		$return .= App::custom_include('sections.'.$layouts_content, $config);
	}else{
		$config['image'] =self::image($config);
		$config['items'] = $items;
		$config['config'] = $config;
	}

	$items_count++;
  endwhile; wp_reset_postdata();

  // if(empty($return)) $return .= \App\template('sections.'.$layouts_content, $config);
  if(empty($return) && isset($layouts_content)) $return .= App::custom_include('sections.'.$layouts_content, $config);
?>
