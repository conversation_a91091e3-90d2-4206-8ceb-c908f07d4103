<?php
$enable_page = get_sub_field('enable_page');

$team_types = get_terms( array(
    'taxonomy' => 'team_type',
    'hide_empty' => true,
));

$placeholder = get_field('team_global','options')['placeholder'];

// WP_Query arguments
$args_team = array (
	'post_type'              => array( 'team' ),
	'post_status'            => array( 'publish' ),
	'nopaging'               => true,
	'order'                  => 'ASC',
	'orderby'                => 'menu_order',
);
$team_members = new WP_Query( $args_team );

$config['class_wrapper'] = App\get_class_grid();
$config['args_team'] = $args_team;
$config['team_members'] = $team_members;
$config['enable_page'] = $enable_page;
$config['team_types'] = $team_types;
$config['placeholder'] = $placeholder;
?>
