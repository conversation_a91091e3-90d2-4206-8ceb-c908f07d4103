<?php
$class_wrapper = "";
$content_align_options = get_field('global','options');
$content_align = get_sub_field('content_align');
if(!$content_align) $content_align = $content_align_options['content_align'];
$content_align_mobile = get_sub_field('content_align_mobile');
if(!$content_align_mobile) $content_align_mobile = $content_align_options['content_align_mobile'];
$image = get_sub_field('image');

$class_wrapper .= 'uk-text-'.$content_align_mobile.' uk-text-'.$content_align;

$args = array (
	'post_type'              => array( 'testimonials' ),
	'post_status'            => array( 'publish' ),
	'nopaging'               => true,
	'order'                  => 'ASC',
	'orderby'                => 'menu_order',
);

if (is_singular('expertise')) {
    $args['meta_query'] = array(
        array(
            'key'       => 'expertise',
            'value'     => get_the_id(),
            'compare'   => 'LIKE',
        ),
    );
}

$testimonials = new WP_Query( $args );

if(\App\block_link('page_careers') == get_the_id()){
    $args = array (
        'post_type'              => array( 'team' ),
        'post_status'            => array( 'publish' ),
        'nopaging'               => true,
        'order'                  => 'ASC',
        'orderby'                => 'menu_order',
        'meta_query'     => array(
            array(
                'key'     => 'testimony',
                'value'   => '',
                'compare' => '!=',
            ),
        ),
    );

    $testimonials = new WP_Query( $args );
}

$config['class_wrapper'] = $class_wrapper;
$config['image'] = $image;
$config['class_prefix'] = 'vy_'.$sections;
$config['testimonials'] = $testimonials;
?>
