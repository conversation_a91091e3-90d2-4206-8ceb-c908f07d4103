<?php
$class_wrapper = "uk-flex-between uk-flex-middle ".App\get_class_grid();

// WP_Query arguments
$args_jobs = array (
	'post_type'              => array( 'job' ),
	'post_status'            => array( 'publish' ),
	'nopaging'               => true,
	'order'                  => 'ASC',
	'orderby'                => 'menu_order',
);
$jobs = new WP_Query( $args_jobs );

$branch_ids_array = array();

if ($jobs->have_posts()) {
    while ($jobs->have_posts()) {
        $jobs->the_post();
        $branch_id = get_field('branch');
        if ($branch_id) {
            $branch_ids_array[] = $branch_id;
        }
    }
    wp_reset_postdata();
}

$branch_ids_array = array_unique($branch_ids_array);

$args_branches = array(
    'post_type'           => 'branch',
    'post_status'         => 'publish',
    'posts_per_page'      => -1,
    'order'       =>  'ASC',
    'post__in' => $branch_ids_array,
);
$branches = new WP_Query( $args_branches );

$config['class_wrapper'] = $class_wrapper;
$config['args_jobs'] = $args_jobs;
$config['jobs'] = $jobs;
$config['branches'] = $branches;
?>
