<?php

use App\Controllers\Partials\Content;
use App\Controllers\App;

// ARGUMENTS
// $args_title = [
//   'class' => $class_prefix .'_title',
//   'tag' => 'h5',
//   'style' => 'uk-heading-divider',
//   'color' => 'primary',
//   'size' => 'uk-h1'
//   'link' => $link,
//   'align' => 'right'
//   'data' => 'data-uk-scrollspy'
// ];
if(!isset($config['style'])) $config['style'] = self::style($config);
if(!isset($config['size'])) $config['size'] = self::size($config);
if(!isset($config['title'])) $config['title'] = self::title($config);
if(!isset($config['color'])) $config['color'] = self::color($config);
if(!isset($config['tag'])) $config['tag'] = self::tag($config);
if(!isset($config['class'])) $class = self::class($config,'vy_title');
if(!isset($config['link'])) $config['link'] = self::link($config);
if(!isset($config['align'])) $config['align'] = self::align($config);
if(!isset($config['data'])) $config['data'] = self::data($config);

if($config['color']) $class .= ' '.$config['color'];
if($config['style']) $class .= ' uk-heading-'.$config['style'];
if($config['size']) $class .= ' '.$config['size'];
if($config['align']) $class .= ' uk-text-'.$config['align'];

// if(isset($config['args']['animation_enable'])) print_r($config['args']['animation_enable']);

$config['animation_enable'] = (isset($config['args']['animation_enable'])) ? $config['args']['animation_enable'] : true;
$config['class'] = $class;
?>
