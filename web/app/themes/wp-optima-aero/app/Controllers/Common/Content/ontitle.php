<?php

use App\Controllers\Partials\Content;

// ARGUMENTS
// $args_ontitle = [
//   'class' => $class_prefix .'_ontitle',
//   'tag' => 'h4',
//   'size' => 'uk-h6'
// ];
if(!isset($config['size'])) $config['size'] = self::size($config,'uk-h4');
if(!isset($config['ontitle'])) $config['ontitle'] = self::ontitle($config);
if(!isset($config['tag'])) $config['tag'] = self::tag($config,'h3');
if(!isset($config['class'])) $class = self::class($config,'vy_ontitle');

if($config['size']) $class .= ' '.$config['size'];

$config['class'] = $class;

?>
