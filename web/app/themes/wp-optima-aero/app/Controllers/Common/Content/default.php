<?php
	// use App\Controllers\Partials\Boilerplate;
  use App\Controllers\Partials\Content;

$return = "";
// pre_print_r($config['items']);
if(get_sub_field('title') || isset($config['items']['title'])){

  $config['size'] = self::size($config);
  $config['title'] = self::title($config);
  $config['balise'] = ((get_sub_field('has_h1'))?'h1':'h2');

  // $return .= \App\template('common.title', $config);
  $return .= App::custom_include('common.title', $config);
}
if(( get_sub_field('text') || get_sub_field('description') ) || isset($config['items']['description']) ){
  // $config['text'] = get_sub_field('text').get_sub_field('description');
   $config['text'] =self::text($config);
  // $return .=  \App\template('common.text', $config);
  $return .= App::custom_include('common.text', $config);
}

if(!in_array('btn',$config['exclude']) ){
  if(get_sub_field('button')){
    $config = get_sub_field('button');
    $return .=  App::button($config);
  }else if(isset($config['items']['button'])){
    $return .=  App::button($config['items']['button']);
  }
}

return $return;
?>
