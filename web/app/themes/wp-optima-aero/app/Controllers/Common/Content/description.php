<?php
use App\Controllers\Partials\Content;

// ARGUMENTS
// $args_text = [
//   'class' => $class_prefix .'_text',
// ];

// if(!isset($config['text'])) $config['text'] = get_sub_field($layout);
$config['override_layout'] = "text";
if(!isset($config['text'])) $config['text'] = self::text($config);
if(!isset($config['class'])) $class = self::class($config,'vy_text uk-panel');

$config['class'] = $class;

?>
