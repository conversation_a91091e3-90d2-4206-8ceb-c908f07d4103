<?php

$style = get_sub_field('style');
$class = "uk-hr";
$image = '';
$height = '';
if($style != 'regular'){
    $class = 'uk-divider-' . $style;
}

if($style == 'image'){
    $style_image = get_sub_field('style_image');
    $height = $style_image['height'];
    $image = $style_image['bg_image'];
    if($height)$class .= ' uk-height-' . $height;
    if($image)$class .= App::get_class_bg($style_image);
}

$config['class'] = $class;
$config['height'] = $height;
$config['image'] = $image;

// return  \App\template('common.'.$layout, $config);
?>
