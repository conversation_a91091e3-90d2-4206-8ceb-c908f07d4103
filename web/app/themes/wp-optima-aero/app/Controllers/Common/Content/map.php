<?php

use App\Controllers\Partials\Content;
use App\Controllers\App;

// ARGUMENTS
// $args_title = [
//   'class' => $class_prefix .'_title',
//   'height' => 'medium',
//   'style' => 'ckzr6nqlf000d14pc8agd4pmn',
//   'coord_contact' => false,
// ];

$class = isset($config['class']) ? $config['class'] : self::class($config, 'vy_map_container');
$height = isset($config['height']) ? $config['height'] : self::height($config);
$multi_address = false;

if($height) $class .= " uk-height-" . $height;

$config['guid'] = \App\get_guid();
if(!isset($config['zoom'])) $config['zoom'] = self::zoom($config);
if(!isset($config['style'])) $config['style'] = self::style($config,get_field('admin_map_style','options'));
$coord_contact = isset($config['coord_contact']) ? $config['coord_contact'] : self::coord_contact($config);

if($coord_contact){

    $addresses = App::get_addresses();

    $address_0 = get_fields($addresses[0]->ID);
    $coord_map = $address_0['map'];

    $coord_map_lat = $coord_map['markers'][0]['lat'];
	$coord_map_lng = $coord_map['markers'][0]['lng'];

    if (count($addresses) > 1){
        foreach ($addresses as $key => $address) {
            $value = get_fields($address->ID);

            $config['markers'][$key]['title'] = get_the_title($address);
            $config['markers'][$key]['address'] = $value['address'];
            if(isset($value['map']['markers']) && $value['map']['markers']){
                $config['markers'][$key]['lat'] = $value['map']['markers'][0]['lat'];
                $config['markers'][$key]['lng'] = $value['map']['markers'][0]['lng'];
            }
        }
		$multi_address = true;
	}
}else if(is_array($config['map']) && $config['map']['lat'] && $config['map']['lng']){
    $coord_map_lat = $config['map']['lat'];
    $coord_map_lng = $config['map']['lng'];
    if (isset($config['map']['markers']) && count($config['map']['markers']) > 1){
        foreach ($config['map']['markers'] as $key => $value) {
            if(isset($value['title'])) $config['markers'][$key]['title'] = $value['title'];
            if(isset($value['phone'])) $config['markers'][$key]['phone'] = $value['phone'];
            $config['markers'][$key]['address'] = $value['label'];
            $config['markers'][$key]['lat'] = $value['lat'];
            $config['markers'][$key]['lng'] = $value['lng'];
        }
        $multi_address = true;
    }else if(isset($config['map']['markers']) && count($config['map']['markers']) === 1){
        $coord_map_lat = $config['map']['markers'][0]['lat'];
        $coord_map_lng = $config['map']['markers'][0]['lng'];
    }
}

$config['coord_map_lat'] = $coord_map_lat;
$config['coord_map_lng'] = $coord_map_lng;

$config['class'] = $class;
$config['multi_address'] = $multi_address;
?>
