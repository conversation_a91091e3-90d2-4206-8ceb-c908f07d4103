<?php

use App\Controllers\Partials\Content;

if(!isset($config['image']['ID']) && function_exists('acf_get_attachment')){
  // $config['image'] = self::image($config);
  if(isset($config['image']['thumbnail_id'])) $config['image'] = acf_get_attachment($config['image']['thumbnail_id']);
  else $config['image'] = self::image($config);

}

// ARGUMENTS
// $args = [
//   'wrapper_class' => $class_prefix .'_image',
//   'wrapper_data' => 'data-uk-scrollspy',
//   'wrapper_enable' => false,
//   'tag_class' => $class_prefix .'_image_tag',
//   'size' => 'thumbnail',
//   'video_enable' => true,
//   'data' => 'data-uk-cover',
//   'is_svg' => 'false'
// ]

$image = [];
$image['ID'] = $config['image']['ID'];

$wrapper_class = 'vy_image';

if(isset($config['args']['wrapper_class']) && !empty(isset($config['args']['wrapper_class']))) $wrapper_class .= ' '.$config['args']['wrapper_class'];
$wrapper_enable = ((isset($config['args']['wrapper_enable']) && !empty(isset($config['args']['wrapper_enable'])))?$config['args']['wrapper_enable']:true);
$video_enable = ((isset($config['args']['video_enable']) && !empty(isset($config['args']['video_enable'])))?$config['args']['video_enable']:true);
$video = get_field('img_video',$image['ID']);
if(!$video && $video_enable) $video_enable = false;

$tag_class = ((isset($config['args']['tag_class']) && !empty(isset($config['args']['tag_class'])))?$config['args']['tag_class']:'');

$wrapper_data = ((isset($config['args']['wrapper_data']) && !empty(isset($config['args']['wrapper_data'])))?$config['args']['wrapper_data']:'');
$data = ((isset($config['args']['data']) && !empty(isset($config['args']['data'])))?$config['args']['data']:'');

$size = ((isset($config['args']['size']) && !empty(isset($config['args']['size'])))?$config['args']['size']:'full');

$image_data = wp_get_attachment_image_src( $image['ID'], 'full', false );
list($src, $width, $height) = $image_data;

$image_meta = wp_get_attachment_metadata( $image['ID'] );
$config['is_svg'] = false;
if(isset($config['args']['is_svg']) && !empty(isset($config['args']['is_svg']))) $config['is_svg'] = $config['args']['is_svg'];
if(!isset($config['args']['is_svg']) && get_post_mime_type($image['ID']) == 'image/svg+xml') $config['is_svg'] = true;


if ( is_array( $image_meta ) ) {
  $size_array = array( absint( $width ), absint( $height ) );
  $srcset     = wp_calculate_image_srcset( $size_array, $src, $image_meta, $image['ID'] );
  $sizes      = wp_calculate_image_sizes( $size_array, $src, $image_meta, $image['ID'] );

  if ( $srcset && ( $sizes || ! empty( $attr['sizes'] ) ) ) {
    $config['srcset'] = $srcset;

    if ( empty( $attr['sizes'] ) ) {
      $config['sizes'] = $sizes;
    }
  }
}

$img_copyright = get_field('img_copyright',$image['ID']);

$aria_label = '';
$alt = '';
$config['role_presentation'] = false;

$image_alt = ((isset($config['args']['alt']) && !empty(isset($config['args']['alt'])))?$config['args']['alt']:$config['image']['alt']);
$image_alt_seo = ((isset($config['args']['aria_label']) && !empty(isset($config['args']['aria_label'])))?$config['args']['aria_label']:get_field('alt_seo',$image['ID']));

if($image_alt){
    $alt = $image_alt;
}
if($image_alt_seo) {
    $alt = $image_alt_seo;
    $config['role_presentation'] = true;
    if($image_alt) {
        $config['role_presentation'] = false;
        $aria_label = $image_alt;
    }
}

$config['aria_label'] = $aria_label;
$config['alt'] = $alt;

$config['size'] = $size;
$config['wrapper_class'] = $wrapper_class;
$config['wrapper_data'] = $wrapper_data;
$config['tag_class'] = $tag_class;
$config['wrapper_enable'] = $wrapper_enable;
$config['video_enable'] = $video_enable;
$config['video'] = $video;
$config['data'] = $data;
$config['img_copyright'] = $img_copyright;

?>
