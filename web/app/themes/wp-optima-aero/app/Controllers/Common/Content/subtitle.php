<?php

use App\Controllers\Partials\Content;

// ARGUMENTS
// $args_subtitle = [
//   'class' => $class_prefix .'_subtitle',
//   'tag' => 'h5',
//   'size' => 'uk-h4'
// ];
if(!isset($config['size'])) $config['size'] = self::size($config,'uk-h4');
if(!isset($config['subtitle'])) $config['subtitle'] = self::subtitle($config);
if(!isset($config['tag'])) $config['tag'] = self::tag($config,'h3');
if(!isset($config['class'])) $class = self::class($config,'vy_subtitle');

if($config['size']) $class .= ' '.$config['size'];

$config['class'] = $class;

?>
