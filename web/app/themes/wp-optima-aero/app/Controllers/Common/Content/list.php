<?php

use App\Controllers\Partials\Content;

$config['class_prefix'] = 'vy_list';
$config['class'] = 'uk-list ' . $config['class_prefix'];

if(isset($config['size']) && $config['size']) $size = $config['size'];
if(!isset($size)) $size = get_sub_field('size');

if(isset($config['style']) && $config['style']) $style = $config['style'];
if(!isset($style)) $style = get_sub_field('style');

if(isset($config['color']) && $config['color']) $color = $config['color'];
if(!isset($color)) $color = get_sub_field('color');


if(isset($size) && $size) $config['class'] .= ' uk-list-'.$size;
if(isset($style) && $style) $config['class'] .= ' uk-list-'.$style;
if(isset($color) && $color) $config['class'] .= ' uk-list-'.$color;

if(get_sub_field('divider') || (isset($config['divider']) && $config['divider'])) $config['class'] .= ' uk-list-divider';

if(!isset($config['list'])) $config['list'] = self::lists();

// $config['balise'] = ((get_sub_field('has_h1'))?'h1':'h2');
?>
