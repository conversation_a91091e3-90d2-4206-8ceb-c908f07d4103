<?php
$class_prefix = 'vy_banner_'.$banners;

$poster = get_sub_field('poster');
$poster_mobile = get_sub_field('poster_mobile');
if(!$poster_mobile) $poster_mobile = $poster;
$img_video = get_sub_field('img_video');

$bg_color = get_sub_field('bg_color');
$class_banner = $class_prefix . " uk-cover-container";
if($bg_color) $class_banner .= " uk-background-" . $bg_color;

$config['img_video'] = $img_video;
$config['class_prefix'] = $class_prefix;
$config['class_banner'] = $class_banner;
$config['height'] = get_sub_field('height');
$config['poster'] = $poster;
$config['poster_mobile'] = $poster_mobile;
$config['video'] = get_sub_field('video');
$config['video_mobile'] = get_sub_field('video_mobile');
$config['disable_video_mobile'] = get_sub_field('disable_video_mobile');
$config['light_enable'] = get_sub_field('light_enable');
$config['content_align'] = get_sub_field('content_align');
$config['content_align_mobile'] = get_sub_field('content_align_mobile');
$config['content'] = get_sub_field('content');
?>
