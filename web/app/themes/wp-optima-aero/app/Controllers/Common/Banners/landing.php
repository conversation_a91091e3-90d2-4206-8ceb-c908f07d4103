<?php
$layout = 'landing';
$class_prefix = 'vy_banner_'.$layout;
$title = get_sub_field('title');
$description = get_sub_field('description');

if(is_tax() && !$title){
  $current_term = get_term_by( 'slug', get_query_var( 'term' ), get_query_var( 'taxonomy' ) );
  $title = $current_term->name;
}elseif(is_404() &&  !$title){
    $title = App\get_string('404_error',__( 'Erreur 404!', TEXTDOMAIN));
}
$bg_image = get_sub_field('bg_image');
$list = get_sub_field('items');
$right_content = get_sub_field('right_content');
$box_bg_color = get_sub_field('bg_color');
$box_light_enable = get_sub_field('light_enable');

if(!$title){
  $title = get_the_title();
}

$class_banner = $class_prefix.' uk-section uk-background-cover';
$class_countainer = 'uk-container uk-container-xlarge vy_banner_'.$layout.'_wrapper';
$class_grid = 'uk-grid uk-grid-large uk-flex-between uk-flex-middle';
$class_content = $class_prefix.'_content uk-light uk-width-1-2@m uk-width-2-5@l';
$class_content_box = $class_prefix.'_box  uk-width-1-2@m uk-width-2-5@l';
$class_content_box_wrapper = $class_prefix.'_box_wrapper uk-tile';
if($box_bg_color){
	$class_content_box_wrapper .= ' uk-background-'.$box_bg_color;
}
if($box_light_enable == '1'){
	$class_content_box_wrapper .= ' uk-light';
}
$class_content_image = $class_prefix.'_image';
$class_title = $class_prefix.'_title';
$class_list = $class_prefix.'_list uk-list uk-list-large uk-list-bullet';
$class_list_item = $class_prefix.'_list_item';
$class_description = $class_prefix.'_description uk-panel uk-width-2-3@m';
$style_banner = '';

if ($bg_image) {
  $class_banner .= ' uk-section-large';
  $style_banner .= 'background-image:url('.$bg_image['url'].')';
}

$config['class_prefix'] = $class_prefix;
$config['title'] = $title;
$config['description'] = $description;
$config['list'] = $list;
$config['image_mobile'] = $bg_image;

$config['right_content'] = $right_content;

$config['class_banner'] = $class_banner;
$config['class_countainer'] = $class_countainer;
$config['class_grid'] = $class_grid;
$config['class_title'] = $class_title;
$config['class_description'] = $class_description;
$config['class_content'] = $class_content;
$config['class_list'] = $class_list;
$config['class_list_item'] = $class_list_item;
$config['class_content_box'] = $class_content_box;
$config['class_content_box_wrapper'] = $class_content_box_wrapper;
$config['class_content_image'] = $class_content_image;

$config['style_banner'] = $style_banner;
?>
