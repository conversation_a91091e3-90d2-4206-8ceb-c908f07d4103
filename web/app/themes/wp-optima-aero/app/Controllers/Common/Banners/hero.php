<?php
$class_prefix = 'vy_banner_'.$banners;
$class_banner = $class_prefix;
$image_collapse = get_sub_field('image_collapse');
$speed = get_sub_field('speed');
if(!$speed) $speed = '6000';

$data_slideshow = 'autoplay: true; autoplay-interval:'.$speed.';';
$grid_align_vertical = get_sub_field('grid_align_vertical');

$slides = get_sub_field('slides');
$light_enable_firstslide = false;
if($slides && $slides[0]['light_enable']) $light_enable_firstslide = $slides[0]['light_enable'];

$class_content = $class_prefix.'_content uk-panel';

$class_grid = $class_prefix.'_wrapper';
$class_grid .= ' ' . App\get_class_grid();

$viewheight_enable = get_sub_field('viewheight_enable');

if($viewheight_enable){
  $class_banner .= ' uk-flex uk-flex-middle uk-child-width-1-1';
}

$config['enable_dotnav'] = get_sub_field('nav_dot');
$config['enable_arrownav'] = get_sub_field('nav_arrow');

$config['data_slideshow'] = $data_slideshow;
$config['slides'] = $slides;
$config['class_prefix'] = $class_prefix;
$config['class_grid'] = $class_grid;
$config['class_banner'] = $class_banner;
$config['image_collapse'] = $image_collapse;
$config['class_content'] = $class_content;
$config['viewheight_enable'] = $viewheight_enable;
$config['light_enable_firstslide'] = $light_enable_firstslide;

?>
