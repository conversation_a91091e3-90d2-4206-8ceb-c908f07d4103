<?php
$class_prefix = 'vy_banner_'.$banners;
$class_prefix_cta = 'vy_banner_cta';
$speed = get_sub_field('speed');
if(!$speed) $speed = '6000';
$cta_enable = get_sub_field('cta_enable');
$slides = get_sub_field('slides');
$light_enable_firstslide = false;
if($slides && $slides[0]['light_enable']) $light_enable_firstslide = $slides[0]['light_enable'];
$cta = get_sub_field('cta');

$class_banner = $class_prefix.' uk-position-relative';
$class_slideshow = 'uk-visible@m uk-slideshow';
$class_slideshow_wrapper = $class_prefix.'_wrapper uk-slideshow-items';
$class_slideshow_content = $class_prefix.'_content uk-panel uk-width-1-2@s uk-position-center-left uk-text-center uk-text-left@s';
$data_slideshow = 'autoplay: true; min-height: 300; max-height: 700;animation: fade;autoplay-interval:'.$speed.';';

if(($cta_enable && $cta)) $class_banner .= ' '.$class_prefix.'-withcta';

$class_cta = $class_prefix_cta;
$class_cta_grid = $class_prefix_cta.'_grid uk-grid uk-grid-collapse uk-text-center';
if($cta){
  $cta_count = count($cta);
  if($cta_count == 4){
    $class_cta_grid .= ' uk-child-width-1-2@m uk-child-width-expand@l';
  }else{
    $class_cta_grid .= ' uk-child-width-expand@m';
  }
}

$config['class_prefix'] = $class_prefix;
$config['class_banner'] = $class_banner;
$config['class_slideshow'] = $class_slideshow;
$config['class_slideshow_wrapper'] = $class_slideshow_wrapper;
$config['class_slideshow_content'] = $class_slideshow_content;
$config['data_slideshow'] = $data_slideshow;


$config['class_prefix_cta'] = $class_prefix_cta;
$config['class_cta'] = $class_cta;
$config['class_cta_grid'] = $class_cta_grid;

$config['speed'] = $speed;
$config['slides'] = $slides;
$config['light_enable_firstslide'] = $light_enable_firstslide;
$config['cta_enable'] = $cta_enable;
$config['cta'] = $cta;
?>
