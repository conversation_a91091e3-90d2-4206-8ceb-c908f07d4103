<?php
$title = get_sub_field('title');
$description = get_sub_field('description');
$height = get_sub_field('height');
$bg_color = get_sub_field('bg_color');
$tag_title = 'h1';
if($bg_color) $bg_color = 'uk-background-'.$bg_color;
if(!$bg_color) $bg_color = 'uk-section-primary';
$light_enable = get_sub_field('light_enable');
$image = get_sub_field('bg_image');



if(is_tax() && !$title){
  $current_term = get_term_by( 'slug', get_query_var( 'term' ), get_query_var( 'taxonomy' ) );
  $title = $current_term->name;
  $thumbnail_id = get_term_meta( $current_term->term_id, 'thumbnail_id', true );
  $image = acf_get_attachment($thumbnail_id);
  if(get_query_var( 'taxonomy' ) == 'product_cat'){
      $image = get_field('bg_image', $current_term);
  }
}elseif(is_404() &&  !$title){
    $title = App\get_string('404_error',__( 'Erreur 404!', TEXTDOMAIN));
}

if(!is_front_page() && is_home() && !$title) $title = get_the_title( get_option('page_for_posts', true) );
if(!$title) $title = get_the_title();

if ( class_exists('WooCommerce') ){

  if(is_shop())  $title = get_the_title(get_option( 'woocommerce_shop_page_id' ));
  if(is_product()) $title = '';
  if(isset($_GET['s']) && !empty($_GET['s'])) $title = __('Search','woocommerce');
  if(is_account_page() && !is_user_logged_in()) $title = __('Connexion / S\'enregistrer',TEXTDOMAIN);
}

$class_banner = 'vy_banner_std uk-section uk-background-cover';
$class_countainer = 'uk-container vy_banner_std_wrapper';
$class_content = 'vy_banner_std_content uk-panel';
$class_title = 'vy_banner_std_title';
$class_description = 'vy_banner_std_description uk-text-lead uk-panel uk-width-2-3@m';
$style_banner = '';

if($image && !$height) $class_banner .= ' uk-section-large';
if ($image) $style_banner .= 'background-image:url('.$image['url'].')';
if($light_enable) $class_banner .= ' uk-light';
$class_banner .= ' '.$bg_color;

if($height){
  $class_banner .= ' uk-height-'.$height;
}


$config['title'] = $title;
$config['tag_title'] = $tag_title;
$config['description'] = $description;
$config['image'] = $image;

$config['class_banner'] = $class_banner;
$config['class_countainer'] = $class_countainer;
$config['class_title'] = $class_title;
$config['class_description'] = $class_description;
$config['class_content'] = $class_content;

$config['style_banner'] = $style_banner;
// $config['bg_image'] = $bg_image;
?>
