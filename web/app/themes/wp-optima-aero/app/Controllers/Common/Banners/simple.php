<?php 
$class_prefix = 'vy_banner_'.$banners;
$title = get_sub_field('title');
$description = get_sub_field('description');
$height = get_sub_field('height');
$light_enable = get_sub_field('light_enable');
$content_enable = get_sub_field('content_enable');
$container = get_sub_field('container');
$bg_color = get_sub_field('bg_color');
$image = get_sub_field('image');
$image_arg = [
  'wrapper_enable' => false,
  'tag_class' => 'uk-width-1-1'
];

if(!$title) $title = get_the_title();
if(!is_front_page() && is_home()) $title = get_the_title( get_option('page_for_posts', true) );


$class_banner = $class_prefix.' uk-background-cover';
$class_countainer = $class_prefix.'_wrapper ';
if($container){
  $class_countainer .= ' uk-container uk-container-'.$container;
}
$class_image = $class_prefix.'_image';
$class_content = $class_prefix.'_content uk-section';
$class_title = $class_prefix.'_title';
$class_description = $class_prefix.'_description uk-panel';

$content_align_options = get_field('global','options');
$content_align = get_sub_field('content_align');
if(!$content_align) $content_align = $content_align_options['content_align'];
$content_align_mobile = get_sub_field('content_align_mobile');
if(!$content_align_mobile) $content_align_mobile = $content_align_options['content_align_mobile'];

$class_content .= ' uk-text-'.$content_align_mobile.' uk-text-'.$content_align;

if($light_enable){
  $class_content .= ' uk-light';
}
if($bg_color){
  $class_content .= ' uk-background-'.$bg_color;
}

$config['title'] = $title;
$config['description'] = $description;
$config['image'] = $image;
$config['image_arg'] = $image_arg;

$config['content_enable'] = $content_enable;
$config['class_banner'] = $class_banner;
$config['class_countainer'] = $class_countainer;
$config['class_title'] = $class_title;
$config['class_description'] = $class_description;
$config['class_image'] = $class_image;
$config['class_content'] = $class_content;
?>