<?php

namespace App\AvsightSync;

/**
 * Handles Salesforce API interactions for Avsight sync
 */
class AvsightApi
{
    /**
     * Gets detailed Salesforce API configuration status
     *
     * @return array Detailed configuration status for each credential.
     */
    public static function getDetailedConfigStatus()
    {
        $config_items = [
            'AVSIGHT_SF_CONSUMER_KEY' => [
                'name' => 'Consumer Key',
                'description' => 'Connected App Consumer Key from Salesforce',
                'value' => defined('AVSIGHT_SF_CONSUMER_KEY') ? AVSIGHT_SF_CONSUMER_KEY : null,
            ],
            'AVSIGHT_SF_CONSUMER_SECRET' => [
                'name' => 'Consumer Secret',
                'description' => 'Connected App Consumer Secret from Salesforce',
                'value' => defined('AVSIGHT_SF_CONSUMER_SECRET') ? AVSIGHT_SF_CONSUMER_SECRET : null,
            ],
            'AVSIGHT_SF_USERNAME' => [
                'name' => 'Username',
                'description' => 'Salesforce API user email address (not needed for client credentials flow)',
                'value' => defined('AVSIGHT_SF_USERNAME') ? AVSIGHT_SF_USERNAME : null,
                'optional' => true,
            ],
            'AVSIGHT_SF_PASSWORD' => [
                'name' => 'Password',
                'description' => 'Salesforce API user password (not needed for client credentials flow)',
                'value' => defined('AVSIGHT_SF_PASSWORD') ? AVSIGHT_SF_PASSWORD : null,
                'optional' => true,
            ],
            'AVSIGHT_SF_SECURITY_TOKEN' => [
                'name' => 'Security Token',
                'description' => 'Salesforce user security token (not needed for client credentials flow)',
                'value' => defined('AVSIGHT_SF_SECURITY_TOKEN') ? AVSIGHT_SF_SECURITY_TOKEN : null,
                'optional' => true,
            ],
            'AVSIGHT_SF_LOGIN_URL' => [
                'name' => 'Login URL',
                'description' => 'Salesforce login endpoint (production or sandbox)',
                'value' => defined('AVSIGHT_SF_LOGIN_URL') ? AVSIGHT_SF_LOGIN_URL : null,
            ],
            'AVSIGHT_SF_API_VERSION' => [
                'name' => 'API Version',
                'description' => 'Salesforce API version (e.g., v55.0)',
                'value' => defined('AVSIGHT_SF_API_VERSION') ? AVSIGHT_SF_API_VERSION : null,
            ],
        ];

        $status = [];
        foreach ($config_items as $constant => $info) {
            $is_configured = !empty($info['value']);
            $is_optional = isset($info['optional']) && $info['optional'];
            $masked_value = '';

            if ($is_configured) {
                // Mask sensitive values for display
                if (in_array($constant, ['AVSIGHT_SF_CONSUMER_SECRET', 'AVSIGHT_SF_PASSWORD', 'AVSIGHT_SF_SECURITY_TOKEN'])) {
                    $masked_value = str_repeat('*', min(strlen($info['value']), 20)) . ' (masked)';
                } else {
                    $masked_value = $info['value'];
                }
            }

            // Determine status based on whether it's optional
            $status_type = 'good';
            $display_value = 'Not configured';

            if ($is_configured) {
                $status_type = 'good';
                $display_value = $masked_value;
            } elseif ($is_optional) {
                $status_type = 'optional';
                $display_value = 'Not needed (optional)';
            } else {
                $status_type = 'error';
                $display_value = 'Not configured';
            }

            $status[$constant] = [
                'name' => $info['name'],
                'description' => $info['description'],
                'configured' => $is_configured,
                'optional' => $is_optional,
                'display_value' => $display_value,
                'status' => $status_type,
            ];
        }

        return $status;
    }

    /**
     * Validates Salesforce API configuration
     *
     * @return bool|\WP_Error True if valid, WP_Error if invalid.
     */
    public static function validateConfig()
    {
        $required_constants = [
            'AVSIGHT_SF_CONSUMER_KEY' => defined('AVSIGHT_SF_CONSUMER_KEY') ? AVSIGHT_SF_CONSUMER_KEY : null,
            'AVSIGHT_SF_CONSUMER_SECRET' => defined('AVSIGHT_SF_CONSUMER_SECRET') ? AVSIGHT_SF_CONSUMER_SECRET : null,
        ];

        foreach ($required_constants as $name => $value) {
            if (empty($value)) {
                return new \WP_Error('missing_config', "Missing required configuration: {$name}");
            }
        }

        return true;
    }

    /**
     * Retrieves Salesforce access token
     *
     * @return string|\WP_Error Access token on success, WP_Error on failure.
     */
    public static function getAccessToken()
    {
        // Validate configuration first
        $config_validation = self::validateConfig();
        if (is_wp_error($config_validation)) {
            return $config_validation;
        }

        $token_url = AVSIGHT_SF_LOGIN_URL;
        $body = array(
            'grant_type'    => 'client_credentials',
            'client_id'     => AVSIGHT_SF_CONSUMER_KEY,
            'client_secret' => AVSIGHT_SF_CONSUMER_SECRET,
        );

        $response = wp_remote_post($token_url, array(
            'method'      => 'POST',
            'timeout'     => 45,
            'redirection' => 5,
            'httpversion' => '1.0',
            'blocking'    => true,
            'headers'     => array(
                'Content-Type' => 'application/x-www-form-urlencoded',
            ),
            'body'        => $body,
        ));

        if (is_wp_error($response)) {
            error_log('Avsight Sync Error: Failed to retrieve Salesforce access token - ' . $response->get_error_message());
            return new \WP_Error('token_request_failed', 'Failed to retrieve Salesforce access token.');
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);

        if ($response_code !== 200) {
            error_log('Avsight Sync Error: Salesforce token request failed with status ' . $response_code . ': ' . $response_body);
            return new \WP_Error('token_request_failed', 'Failed to retrieve Salesforce access token.');
        }

        $token_data = json_decode($response_body, true);

        if (json_last_error() !== JSON_ERROR_NONE || !isset($token_data['access_token'])) {
            error_log('Avsight Sync Error: Invalid JSON response from Salesforce token endpoint: ' . $response_body);
            return new \WP_Error('token_parse_failed', 'Failed to retrieve Salesforce access token.');
        }

        return $token_data['access_token'];
    }

    /**
     * Test API access with a simple query to standard objects
     *
     * @param string $access_token Salesforce access token
     * @return array|\WP_Error Test results on success, WP_Error on failure
     */
    public static function testApiAccess($access_token)
    {
        // Test with a simple query to inscor__Inventory_Line__c (object with inventory quantities)
        $test_query = 'SELECT Id FROM inscor__Inventory_Line__c LIMIT 5';

        $bulk_query_url = str_replace('{version}', AVSIGHT_SF_API_VERSION, AVSIGHT_SF_BULK_QUERY_URL);

        $query_body = array(
            'query' => $test_query,
            'operation' => 'query',
            'contentType' => 'CSV',
            'lineEnding' => 'LF'
        );

        error_log('Avsight Sync Debug: Testing API access with query: ' . $test_query);

        $response = wp_remote_post($bulk_query_url, array(
            'method'      => 'POST',
            'timeout'     => 45,
            'redirection' => 5,
            'httpversion' => '1.0',
            'blocking'    => true,
            'headers'     => array(
                'Authorization' => 'Bearer ' . $access_token,
                'Content-Type'  => 'application/json',
            ),
            'body'        => json_encode($query_body),
        ));

        if (is_wp_error($response)) {
            error_log('Avsight Sync Error: API test failed - ' . $response->get_error_message());
            return new \WP_Error('api_test_failed', 'API test failed: ' . $response->get_error_message());
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);

        error_log('Avsight Sync Debug: API test response code: ' . $response_code);
        error_log('Avsight Sync Debug: API test response body: ' . $response_body);

        // Parse the response to check if job was created successfully
        $job_data = json_decode($response_body, true);
        $success = ($response_code === 201 || $response_code === 200) &&
            isset($job_data['id']) &&
            isset($job_data['state']);

        return [
            'response_code' => $response_code,
            'response_body' => $response_body,
            'job_data' => $job_data,
            'success' => $success
        ];
    }

    /**
     * Test with a very simple query to see what fields are available
     *
     * @param string $access_token Salesforce access token
     * @return array|\WP_Error Test results on success, WP_Error on failure
     */
    public static function testSimpleQuery($access_token)
    {
        // Test with just basic fields - no relationships
        $test_query = 'SELECT Id, inscor__Product__r.Name FROM inscor__Inventory_Line__c LIMIT 5';

        $bulk_query_url = str_replace('{version}', AVSIGHT_SF_API_VERSION, AVSIGHT_SF_BULK_QUERY_URL);

        $query_body = array(
            'query' => $test_query,
            'operation' => 'query',
            'contentType' => 'CSV',
            'lineEnding' => 'LF'
        );

        error_log('Avsight Sync Debug: Testing simple query: ' . $test_query);

        $response = wp_remote_post($bulk_query_url, array(
            'method'      => 'POST',
            'timeout'     => 45,
            'redirection' => 5,
            'httpversion' => '1.0',
            'blocking'    => true,
            'headers'     => array(
                'Authorization' => 'Bearer ' . $access_token,
                'Content-Type'  => 'application/json',
            ),
            'body'        => json_encode($query_body),
        ));

        if (is_wp_error($response)) {
            error_log('Avsight Sync Error: Simple query test failed - ' . $response->get_error_message());
            return new \WP_Error('simple_query_failed', 'Simple query test failed: ' . $response->get_error_message());
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);

        error_log('Avsight Sync Debug: Simple query response code: ' . $response_code);
        error_log('Avsight Sync Debug: Simple query response body: ' . $response_body);

        // Parse the response to check if job was created successfully
        $job_data = json_decode($response_body, true);
        $success = ($response_code === 201 || $response_code === 200) &&
            isset($job_data['id']) &&
            isset($job_data['state']);

        return [
            'response_code' => $response_code,
            'response_body' => $response_body,
            'job_data' => $job_data,
            'success' => $success
        ];
    }

    /**
     * Get list of available objects using REST API describe
     *
     * @param string $access_token Salesforce access token
     * @return array|\WP_Error List of objects on success, WP_Error on failure
     */
    public static function listAvailableObjects($access_token)
    {
        $base_url = str_replace('/services/oauth2/token', '', AVSIGHT_SF_LOGIN_URL);
        $describe_url = $base_url . '/services/data/' . AVSIGHT_SF_API_VERSION . '/sobjects/';

        error_log('Avsight Sync Debug: Describe URL: ' . $describe_url);

        $response = wp_remote_get($describe_url, array(
            'timeout'     => 45,
            'redirection' => 5,
            'httpversion' => '1.0',
            'blocking'    => true,
            'headers'     => array(
                'Authorization' => 'Bearer ' . $access_token,
                'Accept'        => 'application/json',
            ),
        ));

        if (is_wp_error($response)) {
            error_log('Avsight Sync Error: Failed to get object list - ' . $response->get_error_message());
            return new \WP_Error('describe_failed', 'Failed to get object list: ' . $response->get_error_message());
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);

        if ($response_code !== 200) {
            error_log('Avsight Sync Error: Describe failed with status ' . $response_code . ': ' . $response_body);
            return new \WP_Error('describe_failed', 'Failed to get object list. HTTP ' . $response_code);
        }

        $describe_data = json_decode($response_body, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log('Avsight Sync Error: Invalid JSON response from describe endpoint: ' . $response_body);
            return new \WP_Error('describe_parse_failed', 'Failed to parse object list response.');
        }

        // Extract object names, focusing on custom objects
        $objects = [];
        if (isset($describe_data['sobjects'])) {
            foreach ($describe_data['sobjects'] as $object) {
                $objects[] = [
                    'name' => $object['name'],
                    'label' => $object['label'],
                    'custom' => $object['custom'],
                    'queryable' => $object['queryable']
                ];
            }
        }

        // Filter for custom objects that contain 'inscor', 'Inventory', 'AcctSeed', or 'Line'
        $relevant_objects = array_filter($objects, function ($obj) {
            return (stripos($obj['name'], 'inscor') !== false ||
                stripos($obj['name'], 'inventory') !== false ||
                stripos($obj['name'], 'acctseed') !== false ||
                stripos($obj['name'], 'line') !== false ||
                stripos($obj['label'], 'inventory') !== false ||
                stripos($obj['label'], 'line') !== false) &&
                $obj['queryable'];
        });

        error_log('Avsight Sync Debug: Found ' . count($relevant_objects) . ' relevant objects: ' . json_encode($relevant_objects));

        return [
            'all_objects' => $objects,
            'relevant_objects' => $relevant_objects
        ];
    }

    /**
     * Describe a specific object to get its fields
     *
     * @param string $access_token Salesforce access token
     * @param string $object_name Name of the object to describe
     * @return array|\WP_Error Object description on success, WP_Error on failure
     */
    public static function describeObject($access_token, $object_name)
    {
        $base_url = str_replace('/services/oauth2/token', '', AVSIGHT_SF_LOGIN_URL);
        $describe_url = $base_url . '/services/data/' . AVSIGHT_SF_API_VERSION . '/sobjects/' . $object_name . '/describe/';

        error_log('Avsight Sync Debug: Describing object: ' . $object_name);
        error_log('Avsight Sync Debug: Describe URL: ' . $describe_url);

        $response = wp_remote_get($describe_url, array(
            'timeout'     => 45,
            'redirection' => 5,
            'httpversion' => '1.0',
            'blocking'    => true,
            'headers'     => array(
                'Authorization' => 'Bearer ' . $access_token,
                'Accept'        => 'application/json',
            ),
        ));

        if (is_wp_error($response)) {
            error_log('Avsight Sync Error: Failed to describe object - ' . $response->get_error_message());
            return new \WP_Error('describe_object_failed', 'Failed to describe object: ' . $response->get_error_message());
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);

        if ($response_code !== 200) {
            error_log('Avsight Sync Error: Object describe failed with status ' . $response_code . ': ' . $response_body);
            return new \WP_Error('describe_object_failed', 'Failed to describe object. HTTP ' . $response_code);
        }

        $describe_data = json_decode($response_body, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log('Avsight Sync Error: Invalid JSON response from object describe: ' . $response_body);
            return new \WP_Error('describe_object_parse_failed', 'Failed to parse object description.');
        }

        // Extract relevant fields
        $fields = [];
        if (isset($describe_data['fields'])) {
            foreach ($describe_data['fields'] as $field) {
                $fields[] = [
                    'name' => $field['name'] ?? '',
                    'label' => $field['label'] ?? '',
                    'type' => $field['type'] ?? '',
                    'custom' => $field['custom'] ?? false,
                    'queryable' => $field['queryable'] ?? false
                ];
            }
        }

        // Filter for fields that might contain inventory/quantity/product data
        // Include AcctSeed-specific patterns and common inventory fields
        $relevant_fields = array_filter($fields, function ($field) {
            $name_lower = strtolower($field['name']);
            $label_lower = strtolower($field['label']);
            return (stripos($name_lower, 'quantity') !== false ||
                stripos($name_lower, 'product') !== false ||
                stripos($name_lower, 'inventory') !== false ||
                stripos($name_lower, 'stock') !== false ||
                stripos($name_lower, 'available') !== false ||
                stripos($name_lower, 'cost') !== false ||
                stripos($name_lower, 'price') !== false ||
                stripos($name_lower, 'unit') !== false ||
                stripos($name_lower, 'acctseed') !== false ||
                stripos($label_lower, 'quantity') !== false ||
                stripos($label_lower, 'product') !== false ||
                stripos($label_lower, 'inventory') !== false ||
                stripos($label_lower, 'stock') !== false ||
                stripos($label_lower, 'available') !== false ||
                stripos($label_lower, 'cost') !== false ||
                stripos($label_lower, 'price') !== false ||
                stripos($label_lower, 'unit') !== false ||
                // Include basic fields that are always useful
                in_array($name_lower, ['id', 'name', 'createddate', 'lastmodifieddate']) ||
                // Include AcctSeed specific fields we can see
                stripos($name_lower, 'ledger') !== false ||
                stripos($name_lower, 'unit_cost') !== false) &&
                $field['queryable'];
        });

        error_log('Avsight Sync Debug: Found ' . count($relevant_fields) . ' relevant fields for ' . $object_name . ': ' . json_encode($relevant_fields));
        error_log('Avsight Sync Debug: All fields for ' . $object_name . ': ' . json_encode(array_column($fields, 'name')));

        return [
            'object_name' => $object_name,
            'all_fields' => $fields,
            'relevant_fields' => $relevant_fields
        ];
    }

    /**
     * Search for aircraft/helicopter related fields in Salesforce objects
     *
     * @param string $access_token Salesforce access token
     * @return array|\WP_Error Aircraft fields on success, WP_Error on failure
     */
    public static function discoverAircraftFields($access_token)
    {
        $aircraft_keywords = ['aircraft', 'helicopter', 'engine', 'application', 'model', 'type', 'heli', 'part'];
        $objects_to_check = ['inscor__Inventory_Line__c', 'inscor__Product__c', 'Product2'];
        $found_fields = [];

        foreach ($objects_to_check as $object_name) {
            $describe_result = self::describeObject($access_token, $object_name);

            if (is_wp_error($describe_result)) {
                continue;
            }

            foreach ($describe_result['all_fields'] as $field) {
                $field_name_lower = strtolower($field['name']);
                $field_label_lower = strtolower($field['label']);

                // Check if field name or label contains aircraft-related keywords
                foreach ($aircraft_keywords as $keyword) {
                    if (
                        stripos($field_name_lower, $keyword) !== false ||
                        stripos($field_label_lower, $keyword) !== false
                    ) {

                        $found_fields[] = [
                            'object' => $object_name,
                            'field_name' => $field['name'],
                            'field_label' => $field['label'],
                            'field_type' => $field['type'],
                            'queryable' => $field['queryable']
                        ];
                        break;
                    }
                }
            }
        }

        error_log('Avsight Sync: Found aircraft-related fields: ' . json_encode($found_fields));

        return $found_fields;
    }

    /**
     * Sample aircraft field values to see what data they contain
     *
     * @param string $access_token Salesforce access token
     * @return array|\WP_Error Sample values on success, WP_Error on failure
     */
    public static function sampleAircraftFieldValues($access_token)
    {
        $fields_to_sample = [
            'inscor__Repair_Type__c',
            'inscor__Trace_Type__c',
            'inscor__Public_Use_Aircraft__c',
            'inscor__Keyword__c',
            'inscor__Comments__c'
        ];

        $sample_query = 'SELECT Id, ' . implode(', ', $fields_to_sample) . ' ' .
            'FROM inscor__Inventory_Line__c ' .
            'WHERE inscor__Quantity_Available__c > 0 ' .
            'LIMIT 20';

        $bulk_query_url = str_replace('{version}', AVSIGHT_SF_API_VERSION, AVSIGHT_SF_BULK_QUERY_URL);

        $query_body = array(
            'query' => $sample_query,
            'operation' => 'query',
            'contentType' => 'CSV',
            'lineEnding' => 'LF'
        );

        $response = wp_remote_post($bulk_query_url, array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $access_token,
                'Content-Type' => 'application/json'
            ),
            'body' => json_encode($query_body),
            'timeout' => 30
        ));

        if (is_wp_error($response)) {
            return new \WP_Error('api_error', 'Failed to sample aircraft field values: ' . $response->get_error_message());
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);

        if ($response_code !== 200) {
            return new \WP_Error('api_error', 'Failed to sample aircraft field values. HTTP ' . $response_code . ': ' . $response_body);
        }

        $job_data = json_decode($response_body, true);
        if (!$job_data || !isset($job_data['id'])) {
            return new \WP_Error('api_error', 'Invalid response when sampling aircraft field values');
        }

        // Wait a moment for the job to complete
        sleep(2);

        // Get the results
        $results_url = str_replace('{version}', AVSIGHT_SF_API_VERSION, AVSIGHT_SF_BULK_RESULTS_URL);
        $results_url = str_replace('{jobId}', $job_data['id'], $results_url);

        $results_response = wp_remote_get($results_url, array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $access_token,
                'Accept' => 'text/csv'
            ),
            'timeout' => 30
        ));

        if (is_wp_error($results_response)) {
            return new \WP_Error('api_error', 'Failed to get sample results: ' . $results_response->get_error_message());
        }

        $csv_data = wp_remote_retrieve_body($results_response);
        $lines = explode("\n", trim($csv_data));

        if (count($lines) < 2) {
            return new \WP_Error('api_error', 'No sample data returned');
        }

        // Parse CSV
        $headers = str_getcsv($lines[0]);
        $samples = [];

        for ($i = 1; $i < min(count($lines), 11); $i++) { // Max 10 samples
            $row = str_getcsv($lines[$i]);
            if (count($row) === count($headers)) {
                $samples[] = array_combine($headers, $row);
            }
        }

        error_log('Avsight Sync: Aircraft field samples: ' . json_encode($samples));

        return $samples;
    }

    /**
     * Determine if incremental sync should be used
     *
     * @return array Incremental sync configuration
     */
    private static function shouldUseIncrementalSync()
    {
        // Check if incremental sync is enabled in settings
        $incremental_enabled = get_option('avsight_incremental_sync_enabled', true);
        if (!$incremental_enabled) {
            return [
                'enabled' => false,
                'reason' => 'Incremental sync disabled in settings'
            ];
        }

        $last_sync = get_option('avsight_last_successful_sync', false);

        if (!$last_sync) {
            return [
                'enabled' => false,
                'reason' => 'No previous sync timestamp found'
            ];
        }

        // Check if last sync was recent enough (within 30 days)
        $last_sync_time = strtotime($last_sync);
        $thirty_days_ago = time() - (30 * 24 * 60 * 60);

        if ($last_sync_time < $thirty_days_ago) {
            return [
                'enabled' => false,
                'reason' => 'Last sync too old (>30 days), using full sync'
            ];
        }

        return [
            'enabled' => true,
            'last_sync_iso' => $last_sync,
            'last_sync_formatted' => date('Y-m-d H:i:s', $last_sync_time),
            'last_sync_timestamp' => $last_sync_time
        ];
    }

    /**
     * Initiates a bulk query job in Salesforce
     *
     * @param string $access_token Salesforce access token
     * @return string|\WP_Error Job ID on success, WP_Error on failure
     */
    public static function initiateBulkQuery($access_token)
    {
        $bulk_query_url = AVSIGHT_SF_BULK_QUERY_URL; // URL already has version, no replacement needed

        error_log('Avsight Sync Debug: API Version: ' . AVSIGHT_SF_API_VERSION);
        error_log('Avsight Sync Debug: Bulk query URL: ' . $bulk_query_url);

        // Bulk API 2.0 format - no 'object' property needed, it's inferred from the query
        // Use CSV format as this environment doesn't support JSON content type
        // Use inscor__Inventory_Line__c for inventory quantities (now available with correct permissions)

        // Build enhanced query for product merging system (matches API documentation)
        // Pull all fields needed for Part Number + Warehouse + Condition + Certifications grouping
        // Include discovered aircraft-related fields for testing
        $base_query = 'SELECT Id, Name, ' .
            'inscor__Product__c, inscor__Product__r.Name, ' .
            'inscor__Location__c, inscor__Location__r.Name, inscor__Sub_Location__c, ' .
            'inscor__Warehouse__c, inscor__Warehouse__r.Name, ' .
            'inscor__Condition_Code__c, inscor__Condition_Code__r.Name, ' .
            'inscor__Owner_Code__c, inscor__Owner_Code__r.Name, ' .
            'inscor__Serial_Number__c, inscor__Keyword__c, ' .
            'inscor__Quantity_Available__c, inscor__Quantity__c, ' .
            'inscor__Acquisition_Cost__c, inscor__UOM__c, ' .
            'inscor__Batch_LOT__c, inscor__Protected__c, inscor__Protected_Reason__c, ' .
            'inscor__Serialized__c, inscor__Grouping_Enabled__c, ' .
            'inscor__Create_Date__c, inscor__Comments__c, LastModifiedDate, ' .
            // Aircraft-related fields found in discovery
            'inscor__Repair_Type__c, inscor__Trace_Type__c, inscor__Public_Use_Aircraft__c, ' .
            // Aircraft applicability field from Product2 object (multipicklist with aircraft models)
            'inscor__Product__r.inscor__Applicability__c, inscor__Product__r.inscor__Applicability_Detailed__c ' .
            'FROM inscor__Inventory_Line__c';

        // Add filters for valid inventory records (based on API documentation)
        $where_conditions = [];
        $where_conditions[] = "inscor__Product__r.Name != null";
        $where_conditions[] = "inscor__Quantity_Available__c > 0";
        $where_conditions[] = "inscor__Product__c != null";

        // Add condition filters from ACF configuration
        $condition_filter = self::buildConditionFilter();
        if ($condition_filter) {
            $where_conditions[] = $condition_filter;
        }

        // Add hide from services filters (exclude items marked as hidden from REST API)
        $where_conditions[] = "inscor__Owner_Code__r.inscor__Hide_from_Services1__c EXCLUDES ('Rest API')";
        $where_conditions[] = "inscor__Product__r.inscor__Hide_from_Services1__c EXCLUDES ('Rest API')";
        $where_conditions[] = "inscor__Warehouse__r.inscor__Hide_from_Services1__c EXCLUDES ('Rest API')";
        $where_conditions[] = "inscor__Condition_Code__r.inscor__Hide_from_Services1__c EXCLUDES ('Rest API')";

        // Check for incremental sync
        $incremental_sync = self::shouldUseIncrementalSync();
        if ($incremental_sync['enabled']) {
            $where_conditions[] = "LastModifiedDate > " . $incremental_sync['last_sync_iso'];
            error_log('Avsight Sync: Using incremental sync since ' . $incremental_sync['last_sync_formatted']);
        } else {
            error_log('Avsight Sync: Using full sync (no previous sync timestamp)');
        }

        // Add WHERE clause if we have conditions
        if (!empty($where_conditions)) {
            $base_query .= " WHERE " . implode(" AND ", $where_conditions);
        }

        // Add sync limit if in debug mode
        $debug_mode = get_option('avsight_debug_mode', false);
        $sync_limit = get_option('avsight_sync_limit', 0);

        if ($debug_mode && $sync_limit > 0) {
            $base_query .= ' LIMIT ' . (int) $sync_limit;
            error_log('Avsight Sync: Debug mode - limiting to ' . $sync_limit . ' products');
        } else {
            error_log('Avsight Sync: Production mode - no limit (syncing all products)');
        }

        $query_body = array(
            'query' => $base_query,
            'operation' => 'query',
            'contentType' => 'CSV',
            'lineEnding' => 'LF'
        );

        error_log('Avsight Sync Debug: Bulk query URL: ' . $bulk_query_url);
        error_log('Avsight Sync Debug: Bulk query body: ' . json_encode($query_body));
        error_log('Avsight Sync Debug: Access token length: ' . strlen($access_token));

        $response = wp_remote_post($bulk_query_url, array(
            'method'      => 'POST',
            'timeout'     => 45,
            'redirection' => 5,
            'httpversion' => '1.0',
            'blocking'    => true,
            'headers'     => array(
                'Authorization' => 'Bearer ' . $access_token,
                'Content-Type'  => 'application/json',
            ),
            'body'        => json_encode($query_body),
        ));

        if (is_wp_error($response)) {
            error_log('Avsight Sync Error: Failed to initiate bulk query - ' . $response->get_error_message());
            return new \WP_Error('bulk_query_failed', 'Failed to initiate bulk query: ' . $response->get_error_message());
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);

        error_log('Avsight Sync Debug: Bulk query response code: ' . $response_code);
        error_log('Avsight Sync Debug: Bulk query response body: ' . $response_body);

        if ($response_code !== 201 && $response_code !== 200) {
            error_log('Avsight Sync Error: Bulk query initiation failed with status ' . $response_code . ': ' . $response_body);
            return new \WP_Error('bulk_query_failed', 'Failed to initiate bulk query. HTTP ' . $response_code . ': ' . $response_body);
        }

        $job_data = json_decode($response_body, true);

        if (json_last_error() !== JSON_ERROR_NONE || !isset($job_data['id'])) {
            error_log('Avsight Sync Error: Invalid JSON response from bulk query endpoint: ' . $response_body);
            return new \WP_Error('bulk_query_parse_failed', 'Failed to initiate bulk query.');
        }

        return $job_data['id'];
    }

    /**
     * Retrieves bulk query results from Salesforce
     *
     * @param string $access_token Salesforce access token
     * @param string $job_id Bulk query job ID
     * @return array|\WP_Error Query results on success, WP_Error on failure
     */
    public static function getBulkQueryResults($access_token, $job_id)
    {
        error_log('Avsight Sync Debug: BULK_RESULTS_URL template: ' . AVSIGHT_SF_BULK_RESULTS_URL);
        error_log('Avsight Sync Debug: Job ID: ' . $job_id);
        $results_url = str_replace('{jobId}', $job_id, AVSIGHT_SF_BULK_RESULTS_URL); // Only replace jobId, version already in URL
        error_log('Avsight Sync Debug: Final results URL: ' . $results_url);

        $response = wp_remote_get($results_url, array(
            'timeout'     => 45,
            'redirection' => 5,
            'httpversion' => '1.0',
            'blocking'    => true,
            'headers'     => array(
                'Authorization' => 'Bearer ' . $access_token,
                'Accept'        => 'text/csv',
            ),
        ));

        if (is_wp_error($response)) {
            error_log('Avsight Sync Error: Failed to retrieve bulk query results - ' . $response->get_error_message());
            return new \WP_Error('bulk_results_failed', 'Failed to retrieve bulk query results.');
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);

        if ($response_code !== 200) {
            error_log('Avsight Sync Error: Bulk results retrieval failed with status ' . $response_code . ': ' . $response_body);
            return new \WP_Error('bulk_results_failed', 'Failed to retrieve bulk query results.');
        }

        // Parse CSV response instead of JSON
        error_log('Avsight Sync Debug: Raw CSV response body: ' . $response_body);
        error_log('Avsight Sync Debug: CSV response length: ' . strlen($response_body));

        $csv_lines = explode("\n", trim($response_body));
        error_log('Avsight Sync Debug: CSV lines count: ' . count($csv_lines));
        error_log('Avsight Sync Debug: First 3 CSV lines: ' . json_encode(array_slice($csv_lines, 0, 3)));

        if (empty($csv_lines)) {
            error_log('Avsight Sync Error: Empty CSV response from bulk results endpoint');
            return new \WP_Error('bulk_results_parse_failed', 'Empty bulk query results.');
        }

        // Parse CSV header and data
        $header = str_getcsv($csv_lines[0]);
        error_log('Avsight Sync Debug: CSV header: ' . json_encode($header));

        // Debug: Look for aircraft-related fields in the header
        $aircraft_related_fields = [];
        foreach ($header as $field) {
            if (
                stripos($field, 'aircraft') !== false ||
                stripos($field, 'helicopter') !== false ||
                stripos($field, 'applicability') !== false ||
                stripos($field, 'router') !== false ||
                stripos($field, 'model') !== false ||
                stripos($field, 'application') !== false
            ) {
                $aircraft_related_fields[] = $field;
            }
        }

        if (!empty($aircraft_related_fields)) {
            error_log('Avsight Sync Debug: Found potential aircraft-related fields in CSV header: ' . json_encode($aircraft_related_fields));
        } else {
            error_log('Avsight Sync Debug: No obvious aircraft-related fields found in CSV header');
        }

        $results_data = [];

        for ($i = 1; $i < count($csv_lines); $i++) {
            if (trim($csv_lines[$i]) === '') continue; // Skip empty lines

            $row_data = str_getcsv($csv_lines[$i]);
            error_log('Avsight Sync Debug: Row ' . $i . ' data: ' . json_encode($row_data));

            if (count($row_data) === count($header)) {
                $results_data[] = array_combine($header, $row_data);
            } else {
                error_log('Avsight Sync Warning: Row ' . $i . ' column count mismatch. Header: ' . count($header) . ', Row: ' . count($row_data));
            }
        }

        error_log('Avsight Sync Debug: Parsed ' . count($results_data) . ' records from CSV response');

        // Debug: Show aircraft-related field values from first few records
        if (!empty($results_data) && !empty($aircraft_related_fields)) {
            $sample_size = min(3, count($results_data));
            for ($i = 0; $i < $sample_size; $i++) {
                $aircraft_data = [];
                foreach ($aircraft_related_fields as $field) {
                    if (isset($results_data[$i][$field])) {
                        $aircraft_data[$field] = $results_data[$i][$field];
                    }
                }
                if (!empty($aircraft_data)) {
                    error_log('Avsight Sync Debug: Record ' . ($i + 1) . ' aircraft-related data: ' . json_encode($aircraft_data));
                }
            }
        }

        // Debug: Show sample of ALL aircraft applicability data from bulk sync
        $aircraft_applicability_values = [];
        $records_with_applicability = 0;
        foreach ($results_data as $record) {
            if (isset($record['inscor__Product__r.inscor__Applicability__c']) && !empty($record['inscor__Product__r.inscor__Applicability__c'])) {
                $records_with_applicability++;
                $value = $record['inscor__Product__r.inscor__Applicability__c'];
                if (!in_array($value, $aircraft_applicability_values)) {
                    $aircraft_applicability_values[] = $value;
                }
            }
        }

        error_log('Avsight Sync Debug: BULK SYNC aircraft data summary:');
        error_log('Avsight Sync Debug: - Total records: ' . count($results_data));
        error_log('Avsight Sync Debug: - Records with applicability: ' . $records_with_applicability);
        error_log('Avsight Sync Debug: - Unique applicability values: ' . json_encode($aircraft_applicability_values));

        return $results_data;
    }

    /**
     * Check the status of a bulk query job
     *
     * @param string $access_token Salesforce access token
     * @param string $job_id Bulk query job ID
     * @return array|\WP_Error Job status on success, WP_Error on failure
     */
    public static function getBulkQueryJobStatus($access_token, $job_id)
    {
        error_log('Avsight Sync Debug: BULK_QUERY_URL: ' . AVSIGHT_SF_BULK_QUERY_URL);
        error_log('Avsight Sync Debug: Job ID for status: ' . $job_id);
        $job_url = AVSIGHT_SF_BULK_QUERY_URL . '/' . $job_id; // URL already has version, just append job ID
        error_log('Avsight Sync Debug: Final job status URL: ' . $job_url);

        error_log('Avsight Sync Debug: Checking job status. URL: ' . $job_url);

        $response = wp_remote_get($job_url, array(
            'timeout'     => 45,
            'redirection' => 5,
            'httpversion' => '1.0',
            'blocking'    => true,
            'headers'     => array(
                'Authorization' => 'Bearer ' . $access_token,
                'Accept'        => 'application/json',
            ),
        ));

        if (is_wp_error($response)) {
            error_log('Avsight Sync Error: Failed to get job status - ' . $response->get_error_message());
            return new \WP_Error('job_status_failed', 'Failed to get job status: ' . $response->get_error_message());
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);

        error_log('Avsight Sync Debug: Job status response code: ' . $response_code);
        error_log('Avsight Sync Debug: Job status response body: ' . $response_body);

        if ($response_code !== 200) {
            error_log('Avsight Sync Error: Job status check failed with status ' . $response_code . ': ' . $response_body);
            return new \WP_Error('job_status_failed', 'Failed to get job status. HTTP ' . $response_code);
        }

        $job_data = json_decode($response_body, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log('Avsight Sync Error: Invalid JSON response from job status endpoint: ' . $response_body);
            return new \WP_Error('job_status_parse_failed', 'Failed to parse job status response.');
        }

        return $job_data;
    }

    /**
     * Build condition filter from ACF configuration
     *
     * @return string|null Condition filter SQL or null if no conditions configured
     */
    private static function buildConditionFilter()
    {
        // Get condition filters from ACF
        $condition_filters = get_field('cond_filter', 'option');

        if (empty($condition_filters)) {
            // Fallback to default conditions if no ACF configuration
            error_log('Avsight Sync: No condition filters configured in ACF, using default conditions');
            return "inscor__Condition_Code__r.Name IN ('NE','OH','SV','SVAR')";
        }

        $allowed_conditions = [];

        foreach ($condition_filters as $filter) {
            // Only include conditions that are marked to show in summary
            if (!empty($filter['show_in_summary']) && !empty($filter['avsight_condition_code'])) {
                $allowed_conditions[] = "'" . esc_sql($filter['avsight_condition_code']) . "'";
            }
        }

        if (empty($allowed_conditions)) {
            error_log('Avsight Sync: No conditions marked for sync in ACF configuration');
            return null; // No conditions to sync
        }

        $condition_filter = "inscor__Condition_Code__r.Name IN (" . implode(',', $allowed_conditions) . ")";
        error_log('Avsight Sync: Using condition filter from ACF: ' . $condition_filter);

        return $condition_filter;
    }

    /**
     * Get condition display mapping from ACF configuration
     *
     * @return array Mapping of Avsight condition codes to display names
     */
    public static function getConditionDisplayMapping()
    {
        $condition_filters = get_field('cond_filter', 'option');
        $mapping = [];

        if (empty($condition_filters)) {
            // Fallback mapping for default conditions
            return [
                'NE' => 'NE',
                'OH' => 'OH',
                'SV' => 'SV',
                'SVAR' => 'SVAR'
            ];
        }

        foreach ($condition_filters as $filter) {
            if (
                !empty($filter['show_in_summary']) &&
                !empty($filter['avsight_condition_code']) &&
                !empty($filter['display_condition'])
            ) {
                $mapping[$filter['avsight_condition_code']] = $filter['display_condition'];
            }
        }

        return $mapping;
    }

    /**
     * Checks system requirements for Avsight sync
     *
     * @return array System requirements status
     */
    public static function checkSystemRequirements()
    {
        $requirements = [];

        // PHP Version
        $php_version = PHP_VERSION;
        $php_required = '7.4';
        $requirements[] = [
            'name' => 'PHP Version',
            'current' => $php_version,
            'required' => $php_required . '+',
            'status' => version_compare($php_version, $php_required, '>=') ? 'good' : 'error',
            'message' => version_compare($php_version, $php_required, '>=') ? 'Compatible' : 'Upgrade required'
        ];

        // WordPress Version
        $wp_version = get_bloginfo('version');
        $wp_required = '5.0';
        $requirements[] = [
            'name' => 'WordPress Version',
            'current' => $wp_version,
            'required' => $wp_required . '+',
            'status' => version_compare($wp_version, $wp_required, '>=') ? 'good' : 'error',
            'message' => version_compare($wp_version, $wp_required, '>=') ? 'Compatible' : 'Upgrade required'
        ];

        // WooCommerce
        $wc_active = class_exists('WooCommerce');
        $wc_version = $wc_active ? WC()->version : 'Not installed';
        $requirements[] = [
            'name' => 'WooCommerce',
            'current' => $wc_version,
            'required' => '3.0+',
            'status' => $wc_active ? 'good' : 'error',
            'message' => $wc_active ? 'Active' : 'Required plugin not installed'
        ];

        // cURL Extension
        $curl_available = function_exists('curl_init');
        $requirements[] = [
            'name' => 'cURL Extension',
            'current' => $curl_available ? 'Available' : 'Not available',
            'required' => 'Required',
            'status' => $curl_available ? 'good' : 'error',
            'message' => $curl_available ? 'Available' : 'Required for API communication'
        ];

        // JSON Extension
        $json_available = function_exists('json_encode');
        $requirements[] = [
            'name' => 'JSON Extension',
            'current' => $json_available ? 'Available' : 'Not available',
            'required' => 'Required',
            'status' => $json_available ? 'good' : 'error',
            'message' => $json_available ? 'Available' : 'Required for data processing'
        ];

        return $requirements;
    }
}
