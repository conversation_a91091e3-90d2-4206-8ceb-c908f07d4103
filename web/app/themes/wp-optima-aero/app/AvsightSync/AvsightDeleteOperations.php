<?php

namespace App\AvsightSync;

/**
 * Handles bulk product deletion operations
 */
class AvsightDeleteOperations
{
    /**
     * Initiates bulk product deletion
     */
    public static function initBulkDelete()
    {
        error_log('Avsight Sync: Starting bulk product deletion initialization.');

        // Clear any existing deletion transients
        delete_transient(AVSIGHT_BULK_DELETE_TOTAL_ITEMS_KEY);
        delete_transient(AVSIGHT_BULK_DELETE_PROCESSED_ITEMS_KEY);
        delete_transient(AVSIGHT_BULK_DELETE_START_TIME_KEY);
        delete_transient(AVSIGHT_BULK_DELETE_END_TIME_KEY);
        delete_transient('avsight_delete_error');

        // Clear old temp files
        self::deleteTempFileAndTransient(AVSIGHT_BULK_DELETE_IDS_FILE_PATH_KEY);
        self::deleteTempFileAndTransient(AVSIGHT_BULK_DELETE_IDS_FILE_OFFSET_KEY);

        // Get all WooCommerce product IDs
        $all_product_ids = get_posts(array(
            'post_type' => 'product',
            'posts_per_page' => -1,
            'fields' => 'ids',
            'post_status' => 'any'
        ));

        if (empty($all_product_ids)) {
            error_log('Avsight Sync: No products found to delete.');
            return false;
        }

        // Write product IDs to temporary file
        $ids_file_path = self::writeDataToFile(AVSIGHT_BULK_DELETE_IDS_FILE_PATH_KEY, $all_product_ids, true);
        if (!$ids_file_path) {
            error_log('Avsight Sync Error: Failed to write product IDs to temporary file.');
            return false;
        }

        // Set up deletion transients
        set_transient(AVSIGHT_BULK_DELETE_TOTAL_ITEMS_KEY, count($all_product_ids), DAY_IN_SECONDS);
        set_transient(AVSIGHT_BULK_DELETE_PROCESSED_ITEMS_KEY, 0, DAY_IN_SECONDS);
        set_transient(AVSIGHT_BULK_DELETE_START_TIME_KEY, time(), DAY_IN_SECONDS);
        set_transient(AVSIGHT_BULK_DELETE_IDS_FILE_OFFSET_KEY, 0, DAY_IN_SECONDS);

        error_log('Avsight Sync: [Bulk Delete Init] Total WooCommerce products to delete: ' . count($all_product_ids));
        error_log('Avsight Sync: [Bulk Delete Init] Bulk delete initialization complete. AJAX process will pick up from here.');

        return true;
    }

    /**
     * Processes a batch of product deletions via AJAX
     *
     * @param int $offset Starting offset for this batch
     * @return array Response data
     */
    public static function processDeleteBatch($offset = null)
    {
        error_log('Avsight Sync: [AJAX Bulk Delete Batch] AJAX handler hit.');

        // Get current state
        $ids_file_path = get_transient(AVSIGHT_BULK_DELETE_IDS_FILE_PATH_KEY);
        $total_items = (int) get_transient(AVSIGHT_BULK_DELETE_TOTAL_ITEMS_KEY);
        $processed_items = (int) get_transient(AVSIGHT_BULK_DELETE_PROCESSED_ITEMS_KEY);

        // Use provided offset or get from transient
        if ($offset === null) {
            $current_offset = (int) get_transient(AVSIGHT_BULK_DELETE_IDS_FILE_OFFSET_KEY);
        } else {
            $current_offset = (int) $offset;
        }

        if (!$ids_file_path || !file_exists($ids_file_path)) {
            error_log('Avsight Sync Error: [AJAX Bulk Delete Batch] Bulk delete IDs file not found. Process may have been stopped or an error occurred.');
            return [
                'success' => false,
                'message' => 'Bulk delete IDs file not found. Process may have been stopped or an error occurred.'
            ];
        }

        // Read batch of product IDs using configurable batch size
        $delete_batch_size = get_option('avsight_delete_batch_size', AVSIGHT_BULK_DELETE_BATCH_SIZE);
        $file_read_result = self::readLinesFromFile($ids_file_path, $current_offset, $delete_batch_size);
        $batch_product_ids = $file_read_result['items'];
        $next_offset = $file_read_result['next_line_to_process'];

        if (empty($batch_product_ids)) {
            error_log('Avsight Sync: [AJAX Bulk Delete Batch] No more products to delete. Deletion completed.');
            self::completeDeletionProcess();
            return [
                'success' => true,
                'completed' => true,
                'total_items' => $total_items,
                'total_processed' => $processed_items,
                'items_in_batch_processed' => 0,
                'next_offset' => $next_offset
            ];
        }

        // Delete products in this batch
        $batch_deleted = 0;
        foreach ($batch_product_ids as $product_id) {
            $product_id = (int) trim($product_id);
            if ($product_id > 0) {
                $result = wp_delete_post($product_id, true);
                if ($result) {
                    $batch_deleted++;
                    error_log('Avsight Sync: [AJAX Bulk Delete Batch] Deleted product ID: ' . $product_id);
                } else {
                    error_log('Avsight Sync Warning: [AJAX Bulk Delete Batch] Failed to delete product ID: ' . $product_id);
                }
            }
        }

        // Update progress
        $new_processed_count = $processed_items + $batch_deleted;
        set_transient(AVSIGHT_BULK_DELETE_PROCESSED_ITEMS_KEY, $new_processed_count, DAY_IN_SECONDS);
        set_transient(AVSIGHT_BULK_DELETE_IDS_FILE_OFFSET_KEY, $next_offset, DAY_IN_SECONDS);

        error_log('Avsight Sync: [AJAX Bulk Delete Batch] Batch processed. Deleted: ' . $batch_deleted . '/' . count($batch_product_ids) . '. Total progress: ' . $new_processed_count . '/' . $total_items);

        // Check if completed
        $completed = ($next_offset >= $total_items) || ($new_processed_count >= $total_items);

        if ($completed) {
            self::completeDeletionProcess();
        }

        return [
            'success' => true,
            'completed' => $completed,
            'total_items' => $total_items,
            'total_processed' => $new_processed_count,
            'items_in_batch_processed' => $batch_deleted,
            'next_offset' => $next_offset
        ];
    }

    /**
     * Stops the bulk deletion process
     */
    public static function stopBulkDelete()
    {
        error_log('Avsight Sync: Manual bulk deletion stop initiated. All deletion transients and scheduled batches cleared.');

        // Set end time
        set_transient(AVSIGHT_BULK_DELETE_END_TIME_KEY, time(), DAY_IN_SECONDS);

        // Clean up temporary files
        self::deleteTempFileAndTransient(AVSIGHT_BULK_DELETE_IDS_FILE_PATH_KEY);
        delete_transient(AVSIGHT_BULK_DELETE_IDS_FILE_OFFSET_KEY);

        return [
            'success' => true,
            'message' => 'Bulk product deletion process stopped.'
        ];
    }

    /**
     * Gets the current deletion status
     *
     * @return array Deletion status information
     */
    public static function getDeleteStatus()
    {
        $total_items = (int) get_transient(AVSIGHT_BULK_DELETE_TOTAL_ITEMS_KEY);
        $processed_items = (int) get_transient(AVSIGHT_BULK_DELETE_PROCESSED_ITEMS_KEY);
        $start_time = get_transient(AVSIGHT_BULK_DELETE_START_TIME_KEY);
        $end_time = get_transient(AVSIGHT_BULK_DELETE_END_TIME_KEY);

        $is_running = $start_time && !$end_time && $total_items > 0 && $processed_items < $total_items;

        $status = [
            'running' => $is_running,
            'total' => $total_items,
            'processed' => $processed_items,
            'percentage' => $total_items > 0 ? round(($processed_items / $total_items) * 100, 2) : 0,
            'time_elapsed' => 0,
            'time_remaining' => 'N/A',
            'next_batch_in' => 1, // 1 second delay between batches
        ];

        if ($start_time) {
            $current_time = $end_time ? $end_time : time();
            $status['time_elapsed'] = $current_time - $start_time;

            if ($is_running && $processed_items > 0) {
                $items_per_second = $processed_items / $status['time_elapsed'];
                $remaining_items = $total_items - $processed_items;
                $estimated_seconds = $items_per_second > 0 ? $remaining_items / $items_per_second : 0;
                $status['time_remaining'] = self::formatTime($estimated_seconds);
            }
        }

        return $status;
    }

    /**
     * Completes the deletion process and cleans up
     */
    private static function completeDeletionProcess()
    {
        error_log('Avsight Sync: Bulk product deletion completed successfully.');

        // Set end time
        set_transient(AVSIGHT_BULK_DELETE_END_TIME_KEY, time(), DAY_IN_SECONDS);

        // Clean up temporary files
        self::deleteTempFileAndTransient(AVSIGHT_BULK_DELETE_IDS_FILE_PATH_KEY);
        delete_transient(AVSIGHT_BULK_DELETE_IDS_FILE_OFFSET_KEY);

        // Clear any deletion errors
        delete_transient('avsight_delete_error');
    }

    /**
     * Writes data to a temporary file
     *
     * @param string $file_path_key Transient key for file path
     * @param array $data Data to write
     * @param bool $json_lines Whether to write as JSON lines format
     * @return string|false File path on success, false on failure
     */
    private static function writeDataToFile($file_path_key, $data, $json_lines = false)
    {
        $upload_dir = wp_upload_dir();
        $temp_dir = $upload_dir['basedir'] . '/avsight-sync-temp/';

        if (!file_exists($temp_dir)) {
            wp_mkdir_p($temp_dir);
        }

        $file_name = str_replace('avsight_', '', $file_path_key) . '.jsonl';
        $file_path = $temp_dir . $file_name;

        $file_handle = fopen($file_path, 'w');
        if (!$file_handle) {
            error_log('Avsight Sync Error: Could not open file for writing: ' . $file_path);
            return false;
        }

        if ($json_lines) {
            foreach ($data as $item) {
                fwrite($file_handle, json_encode($item) . "\n");
            }
        } else {
            fwrite($file_handle, json_encode($data));
        }

        fclose($file_handle);

        set_transient($file_path_key, $file_path, DAY_IN_SECONDS);
        error_log('Avsight Sync: Successfully wrote ' . count($data) . ' items to temporary file: ' . $file_path . ' for key: ' . $file_path_key);

        return $file_path;
    }

    /**
     * Reads lines from a file
     *
     * @param string $file_path Path to file
     * @param int $start_line Starting line number (0-based)
     * @param int $max_lines Maximum lines to read
     * @return array Array with 'items' and 'next_line_to_process'
     */
    private static function readLinesFromFile($file_path, $start_line, $max_lines)
    {
        $items = [];
        $current_line = 0;
        $lines_read = 0;

        $file_handle = fopen($file_path, 'r');
        if (!$file_handle) {
            error_log('Avsight Sync Error: Could not open file for reading: ' . $file_path);
            return ['items' => [], 'next_line_to_process' => $start_line];
        }

        while (($line = fgets($file_handle)) !== false && $lines_read < $max_lines) {
            if ($current_line >= $start_line) {
                $item = json_decode(trim($line), true);
                if ($item !== null) {
                    $items[] = $item;
                } else {
                    // If JSON decode fails, treat as plain text (for product IDs)
                    $items[] = trim($line);
                }
                $lines_read++;
            }
            $current_line++;
        }

        fclose($file_handle);

        return [
            'items' => $items,
            'next_line_to_process' => $start_line + $lines_read
        ];
    }

    /**
     * Deletes temporary file and associated transient
     *
     * @param string $file_path_key Transient key for file path
     * @param string $offset_key Optional offset transient key to delete
     */
    private static function deleteTempFileAndTransient($file_path_key, $offset_key = null)
    {
        $file_path = get_transient($file_path_key);
        if ($file_path && file_exists($file_path)) {
            unlink($file_path);
            error_log('Avsight Sync: Deleted temporary file: ' . $file_path);
        }

        delete_transient($file_path_key);

        if ($offset_key) {
            delete_transient($offset_key);
        }
    }

    /**
     * Formats time in seconds to HH:MM:SS format
     *
     * @param int $seconds Time in seconds
     * @return string Formatted time string
     */
    private static function formatTime($seconds)
    {
        // Ensure input is numeric and convert to float first
        $seconds = (float) $seconds;

        if ($seconds <= 0) {
            return '00:00:00';
        }

        $hours = (int) floor($seconds / 3600);
        $minutes = (int) floor(($seconds % 3600) / 60);
        $seconds = (int) floor($seconds % 60);

        return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    }
}
