<?php

namespace App\AvsightSync;

/**
 * Handles product merging logic for Avsight inventory sync
 * Groups products by Part Number + Warehouse + Condition + Certifications
 * Merges quantities and creates unified product data
 */
class AvsightProductMerger
{
    /**
     * Group and merge inventory items by key attributes
     *
     * @param array $inventory_items Raw inventory data from Salesforce
     * @return array Merged product groups
     */
    public static function groupAndMergeProducts($inventory_items)
    {
        if (empty($inventory_items)) {
            return [];
        }

        error_log('Avsight Merger: Starting to group ' . count($inventory_items) . ' inventory items');

        $grouped_products = [];
        $processed_count = 0;
        $skipped_count = 0;

        foreach ($inventory_items as $item) {
            $processed_count++;

            // Extract key data for grouping
            $grouping_data = self::extractGroupingData($item);

            if (!$grouping_data) {
                $skipped_count++;
                error_log('Avsight Merger: Skipped item ' . $processed_count . ' - missing required data: ' . json_encode($item));
                continue;
            }

            // Create unique key for grouping
            $group_key = self::createGroupKey($grouping_data);

            // Initialize group if it doesn't exist
            if (!isset($grouped_products[$group_key])) {
                $grouped_products[$group_key] = self::initializeProductGroup($grouping_data, $item);
            }

            // Add this item to the group
            self::addItemToGroup($grouped_products[$group_key], $item);
        }

        error_log('Avsight Merger: Processed ' . $processed_count . ' items, skipped ' . $skipped_count . ', created ' . count($grouped_products) . ' product groups');

        return array_values($grouped_products); // Return indexed array
    }

    /**
     * Extract key data needed for product grouping
     *
     * @param array $item Single inventory item from Salesforce
     * @return array|false Grouping data or false if invalid
     */
    private static function extractGroupingData($item)
    {
        // Required fields for grouping
        $part_number = self::getPartNumber($item);
        $warehouse = self::getWarehouse($item);
        $condition = self::getCondition($item);

        if (!$part_number || !$warehouse || !$condition) {
            return false;
        }

        return [
            'part_number' => $part_number,
            'warehouse' => $warehouse,
            'condition' => $condition,
            'certifications' => self::getCertifications($item), // Optional
            'product_name' => self::getProductName($item),
            'product_id' => self::getProductId($item)
        ];
    }

    /**
     * Create unique grouping key
     *
     * @param array $grouping_data Extracted grouping data
     * @return string Unique key for grouping
     */
    private static function createGroupKey($grouping_data)
    {
        // Group by: Part Number + Warehouse + Condition + Certifications
        $key_parts = [
            'part' => sanitize_title($grouping_data['part_number']),
            'warehouse' => sanitize_title($grouping_data['warehouse']),
            'condition' => sanitize_title($grouping_data['condition']),
            'cert' => sanitize_title($grouping_data['certifications'] ?? 'none')
        ];

        return implode('|', $key_parts);
    }

    /**
     * Initialize a new product group
     *
     * @param array $grouping_data Grouping data
     * @param array $first_item First item in the group
     * @return array Initialized product group
     */
    private static function initializeProductGroup($grouping_data, $first_item)
    {
        return [
            // Grouping identifiers
            'part_number' => $grouping_data['part_number'],
            'warehouse' => $grouping_data['warehouse'],
            'condition' => $grouping_data['condition'],
            'certifications' => $grouping_data['certifications'],

            // Product information
            'product_name' => $grouping_data['product_name'],
            'product_id' => $grouping_data['product_id'],
            'description' => self::getDescription($first_item),

            // Aggregated data
            'total_quantity' => 0,
            'total_cost' => 0,
            'average_cost' => 0,
            'serial_numbers' => [],
            'inventory_items' => [],

            // Additional fields from first item
            'location' => self::getLocation($first_item),
            'uom' => self::getUOM($first_item),
            'batch_lot' => self::getBatchLot($first_item),
            'protected' => self::getProtected($first_item),
            'protected_reason' => self::getProtectedReason($first_item),
            'serialized' => self::getSerialized($first_item),

            // Aircraft/helicopter information for WooCommerce attributes
            'aircraft_model' => self::getAircraftModel($first_item),
            'engine_type' => self::getEngineType($first_item),

            // Metadata
            'item_count' => 0,
            'last_modified' => self::getLastModified($first_item)
        ];
    }

    /**
     * Add an inventory item to an existing product group
     *
     * @param array &$group Product group (passed by reference)
     * @param array $item Inventory item to add
     */
    private static function addItemToGroup(&$group, $item)
    {
        $quantity = self::getQuantity($item);
        $cost = self::getCost($item);
        $serial_number = self::getSerialNumber($item);
        $last_modified = self::getLastModified($item);

        // Update aggregated quantities
        $group['total_quantity'] += $quantity;

        // Update cost calculations
        if ($cost > 0) {
            $group['total_cost'] += ($cost * $quantity);
        }

        // Calculate average cost
        if ($group['total_quantity'] > 0 && $group['total_cost'] > 0) {
            $group['average_cost'] = $group['total_cost'] / $group['total_quantity'];
        }

        // Add serial number if present and unique
        if ($serial_number && !in_array($serial_number, $group['serial_numbers'])) {
            $group['serial_numbers'][] = $serial_number;
        }

        // Store individual item data
        $group['inventory_items'][] = [
            'id' => $item['Id'] ?? '',
            'name' => $item['Name'] ?? '',
            'quantity' => $quantity,
            'cost' => $cost,
            'serial_number' => $serial_number,
            'last_modified' => $last_modified
        ];

        // Update metadata
        $group['item_count']++;

        // Keep the most recent modification date
        if ($last_modified && (!$group['last_modified'] || $last_modified > $group['last_modified'])) {
            $group['last_modified'] = $last_modified;
        }
    }

    /**
     * Extract part number from inventory item using configurable rules
     */
    private static function getPartNumber($item)
    {
        // Get configurable field path
        $rules = \App\AvsightSync\AvsightAdmin::getProductMergingRules();
        $field_path = $rules['part_number_field'];

        $value = self::getFieldValue($item, $field_path);

        // Fallback to default fields if configured field is empty
        if (empty($value)) {
            $value = $item['inscor__Product__r.inscor__Part_Number__c'] ??
                $item['inscor__Product__r.Name'] ??
                $item['inscor__Product__c'] ??
                null;
        }

        return $value;
    }

    /**
     * Get field value from item using dot notation path
     */
    private static function getFieldValue($item, $field_path)
    {
        if (empty($field_path)) {
            return '';
        }

        // Handle nested field access (e.g., 'inscor__Product__r.Name')
        if (strpos($field_path, '.') !== false) {
            $parts = explode('.', $field_path);
            $value = $item;

            foreach ($parts as $part) {
                if (isset($value[$part])) {
                    $value = $value[$part];
                } else {
                    return '';
                }
            }

            return is_string($value) ? $value : '';
        }

        // Direct field access
        return $item[$field_path] ?? '';
    }

    /**
     * Extract warehouse/location from inventory item using configurable rules
     */
    private static function getWarehouse($item)
    {
        // Get configurable field path
        $rules = \App\AvsightSync\AvsightAdmin::getProductMergingRules();
        $field_path = $rules['warehouse_field'];

        $value = self::getFieldValue($item, $field_path);

        // Fallback to default fields if configured field is empty
        if (empty($value)) {
            $value = $item['inscor__Warehouse__r.Name'] ??
                $item['inscor__Location__c'] ??
                null;
        }

        return $value;
    }

    /**
     * Extract condition from inventory item using configurable rules
     */
    private static function getCondition($item)
    {
        // Get configurable field path
        $rules = \App\AvsightSync\AvsightAdmin::getProductMergingRules();
        $field_path = $rules['condition_field'];

        $value = self::getFieldValue($item, $field_path);

        // Fallback to default fields if configured field is empty
        if (empty($value)) {
            $value = $item['inscor__Condition_Code__r.Name'] ??
                $item['inscor__Condition_Code__c'] ??
                null;
        }

        return $value;
    }

    /**
     * Extract certifications from inventory item
     */
    private static function getCertifications($item)
    {
        // Use Owner Code as primary certification, with keyword as fallback
        $certifications = [];

        if (!empty($item['inscor__Owner_Code__r.Name'])) {
            $certifications[] = $item['inscor__Owner_Code__r.Name'];
        }

        if (!empty($item['inscor__Keyword__c'])) {
            $certifications[] = $item['inscor__Keyword__c'];
        }

        return !empty($certifications) ? implode(' | ', $certifications) : null;
    }

    /**
     * Extract product name from inventory item
     */
    private static function getProductName($item)
    {
        return $item['inscor__Product__r.Name'] ?? null;
    }

    /**
     * Extract product ID from inventory item
     */
    private static function getProductId($item)
    {
        return $item['inscor__Product__c'] ?? null;
    }

    /**
     * Extract description from inventory item
     */
    private static function getDescription($item)
    {
        return $item['inscor__Product__r.Name'] ??
            $item['inscor__Keyword__c'] ??
            $item['inscor__Comments__c'] ??
            null;
    }

    /**
     * Extract quantity from inventory item
     */
    private static function getQuantity($item)
    {
        return (float) ($item['inscor__Quantity_Available__c'] ?? 0);
    }

    /**
     * Extract cost from inventory item
     */
    private static function getCost($item)
    {
        return (float) ($item['inscor__Acquisition_Cost__c'] ?? 0);
    }

    /**
     * Extract serial number from inventory item
     */
    private static function getSerialNumber($item)
    {
        return $item['inscor__Serial_Number__c'] ?? null;
    }

    /**
     * Extract last modified date from inventory item
     */
    private static function getLastModified($item)
    {
        return $item['LastModifiedDate'] ?? null;
    }

    /**
     * Extract location from inventory item
     */
    private static function getLocation($item)
    {
        return $item['inscor__Location__r.Name'] ?? $item['inscor__Location__c'] ?? null;
    }

    /**
     * Extract unit of measure from inventory item
     */
    private static function getUOM($item)
    {
        return $item['inscor__UOM__c'] ?? null;
    }

    /**
     * Extract batch/LOT from inventory item
     */
    private static function getBatchLot($item)
    {
        return $item['inscor__Batch_LOT__c'] ?? null;
    }

    /**
     * Extract protected status from inventory item
     */
    private static function getProtected($item)
    {
        return filter_var($item['inscor__Protected__c'] ?? false, FILTER_VALIDATE_BOOLEAN);
    }

    /**
     * Extract protected reason from inventory item
     */
    private static function getProtectedReason($item)
    {
        return $item['inscor__Protected_Reason__c'] ?? null;
    }

    /**
     * Extract serialized status from inventory item
     */
    private static function getSerialized($item)
    {
        return filter_var($item['inscor__Serialized__c'] ?? false, FILTER_VALIDATE_BOOLEAN);
    }

    /**
     * Extract aircraft model from inventory item
     */
    private static function getAircraftModel($item)
    {
        // Debug: Log the structure of the item to see how aircraft data is formatted
        static $debug_count = 0;
        if ($debug_count < 3) { // Only log first 3 items to avoid spam
            $debug_count++;
            error_log('Avsight Sync Debug: getAircraftModel() item structure for record ' . $debug_count . ':');
            error_log('Avsight Sync Debug: - Product Name: ' . ($item['inscor__Product__r']['Name'] ?? 'N/A'));
            error_log('Avsight Sync Debug: - Raw applicability field: ' . json_encode($item['inscor__Product__r.inscor__Applicability__c'] ?? 'NOT_SET'));
            error_log('Avsight Sync Debug: - Nested applicability field: ' . json_encode($item['inscor__Product__r']['inscor__Applicability__c'] ?? 'NOT_SET'));
            error_log('Avsight Sync Debug: - All Product__r fields: ' . json_encode(array_keys($item['inscor__Product__r'] ?? [])));
        }

        // Primary field: Product Applicability (multipicklist with aircraft models like AS350B2, Bell 212, etc.)
        // Try both flat and nested field access patterns
        $applicability = $item['inscor__Product__r.inscor__Applicability__c'] ??
            $item['inscor__Product__r']['inscor__Applicability__c'] ?? null;
        if (!empty($applicability)) {
            error_log('Avsight Sync Debug: Found applicability data: ' . $applicability);
            // Apply application code mapping
            $mapped_value = \App\AvsightSync\AvsightAdmin::applyApplicationCodeMapping($applicability);
            error_log('Avsight Sync Debug: Mapped applicability data: ' . $mapped_value);
            return $mapped_value;
        }

        // Secondary field: Detailed Applicability
        $applicability_detailed = $item['inscor__Product__r.inscor__Applicability_Detailed__c'] ??
            $item['inscor__Product__r']['inscor__Applicability_Detailed__c'] ?? null;
        if (!empty($applicability_detailed)) {
            error_log('Avsight Sync Debug: Found detailed applicability data: ' . $applicability_detailed);
            return $applicability_detailed;
        }

        // Fallback to other aircraft-related fields
        $fallback = $item['inscor__Repair_Type__c'] ??
            $item['inscor__Trace_Type__c'] ??
            $item['inscor__Product__r.inscor__Aircraft_Model__c'] ??
            $item['inscor__Product__r.inscor__Application__c'] ??
            $item['inscor__Product__r.inscor__Helicopter_Type__c'] ??
            $item['inscor__Aircraft_Type__c'] ??
            $item['inscor__Application_Code__c'] ??
            $item['inscor__Helicopter_Model__c'] ??
            null;

        if (!empty($fallback)) {
            error_log('Avsight Sync Debug: Using fallback aircraft data: ' . $fallback);
        }

        return $fallback;
    }

    /**
     * Extract engine type from inventory item
     */
    private static function getEngineType($item)
    {
        return $item['inscor__Engine_Type__c'] ??
            $item['inscor__Product__r.inscor__Engine_Type__c'] ??
            null;
    }

    /**
     * Generate WooCommerce-compatible SKU from grouped product
     *
     * @param array $group Merged product group
     * @return string Generated SKU
     */
    public static function generateSku($group)
    {
        // Use display condition name from standalone mapping configuration for SKU
        $raw_condition = $group['condition'];
        $display_condition = \App\AvsightSync\AvsightAdmin::applyConditionCodeMapping($raw_condition);

        $sku_parts = [
            sanitize_title($group['part_number']),
            sanitize_title($group['warehouse']),
            sanitize_title($display_condition)
        ];

        if (!empty($group['certifications'])) {
            $sku_parts[] = sanitize_title($group['certifications']);
        }

        return strtoupper(implode('-', $sku_parts));
    }

    /**
     * Generate WooCommerce product title from grouped product
     *
     * @param array $group Merged product group
     * @return string Generated product title
     */
    public static function generateProductTitle($group)
    {
        $title_parts = [$group['part_number']];

        if ($group['condition']) {
            // Use display condition name from standalone mapping configuration
            $raw_condition = $group['condition'];
            $display_condition = \App\AvsightSync\AvsightAdmin::applyConditionCodeMapping($raw_condition);

            $title_parts[] = '(' . $display_condition . ')';
        }

        if ($group['warehouse']) {
            $title_parts[] = '- ' . $group['warehouse'];
        }

        return implode(' ', $title_parts);
    }
}
