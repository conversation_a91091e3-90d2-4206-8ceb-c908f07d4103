<?php

namespace App\AvsightSync;

/**
 * Main Avsight Sync Controller
 * Coordinates all sync operations and initializes components
 */
class AvsightSync
{
    /**
     * Initialize the Avsight Sync system
     */
    public static function init()
    {
        // Initialize components
        AvsightAdmin::init();
        AvsightAjax::init();
        AvsightScheduler::init();

        // Register WordPress hooks
        self::registerHooks();

        // Enqueue styles and scripts
        add_action('admin_enqueue_scripts', [self::class, 'enqueueAssets']);
    }

    /**
     * Register WordPress hooks and actions
     */
    private static function registerHooks()
    {
        // Cron job for batch processing
        add_action('avsight_process_inventory_batch_event', [AvsightSyncOperations::class, 'processInventoryBatch']);

        // Admin notices
        add_action('admin_notices', [self::class, 'showAdminNotices']);
    }

    /**
     * Enqueue CSS and JavaScript for admin pages
     */
    public static function enqueueAssets($hook)
    {
        if ($hook !== 'toplevel_page_avsight-sync') {
            return;
        }

        // Add inline CSS
        wp_add_inline_style('wp-admin', self::getInlineCSS());

        // Add inline JavaScript
        wp_add_inline_script('jquery', self::getInlineJavaScript());
    }

    /**
     * Get inline CSS for admin interface
     */
    private static function getInlineCSS()
    {
        return '
            /* Avsight Sync Admin Styles */
            .avsight-tab-content {
                margin-top: 20px;
            }

            .avsight-status-box {
                background: #fff;
                border: 1px solid #ccd0d4;
                border-radius: 4px;
                padding: 20px;
                margin-bottom: 20px;
                box-shadow: 0 1px 1px rgba(0,0,0,.04);
            }

            .avsight-status-box h3 {
                margin-top: 0;
                margin-bottom: 15px;
                font-size: 16px;
                color: #23282d;
            }

            .avsight-progress-bar {
                width: 100%;
                height: 25px;
                background-color: #f1f1f1;
                border-radius: 3px;
                overflow: hidden;
                margin: 10px 0;
                border: 1px solid #ddd;
            }

            .avsight-progress-bar-inner {
                height: 100%;
                background-color: #0073aa;
                color: white;
                text-align: center;
                line-height: 25px;
                font-size: 12px;
                font-weight: bold;
                transition: width 0.3s ease;
                min-width: 30px;
            }

            .status-good {
                color: #46b450;
                font-weight: bold;
            }

            .status-warning {
                color: #ffb900;
                font-weight: bold;
            }

            .status-error {
                color: #dc3232;
                font-weight: bold;
            }

            /* Inline Log Styles */
            .avsight-log-section {
                margin-top: 15px;
                border-top: 1px solid #ddd;
                padding-top: 15px;
            }

            .avsight-log-section h4 {
                margin: 0 0 10px 0;
                font-size: 14px;
                color: #333;
            }

            .avsight-log-content-inline {
                background-color: #1e1e1e;
                color: #f0f0f0;
                font-family: "Courier New", monospace;
                font-size: 11px;
                padding: 10px;
                border-radius: 3px;
                height: 200px;
                overflow-y: auto;
                margin-bottom: 10px;
                white-space: pre-wrap;
                word-wrap: break-word;
                border: 1px solid #ccc;
            }

            .avsight-log-controls-inline {
                display: flex;
                gap: 5px;
                justify-content: flex-end;
            }

            /* Log entry styles */
            .avsight-log-entry {
                margin-bottom: 2px;
                line-height: 1.4;
            }

            .avsight-log-timestamp {
                color: #888;
                font-size: 10px;
            }

            .avsight-log-level-info {
                color: #4CAF50;
            }

            .avsight-log-level-warning {
                color: #FF9800;
            }

            .avsight-log-level-error {
                color: #F44336;
            }

            .avsight-log-level-success {
                color: #8BC34A;
                font-weight: bold;
            }

            /* Table styles */
            .widefat th {
                font-weight: 600;
            }

            .status-good td {
                color: #46b450;
            }

            .status-error td {
                color: #dc3232;
            }

            .status-optional td {
                color: #666;
            }

            /* Button styles */
            .button-danger {
                background-color: #dc3232 !important;
                border-color: #dc3232 !important;
                color: white !important;
            }

            .button-danger:hover {
                background-color: #c62d2d !important;
                border-color: #c62d2d !important;
            }

            .button-small {
                height: auto;
                padding: 4px 8px;
                font-size: 11px;
                line-height: 1.4;
            }
        ';
    }

    /**
     * Get inline JavaScript for admin interface
     */
    private static function getInlineJavaScript()
    {
        // Get batch sizes from PHP options
        $sync_batch_size = (int) get_option('avsight_sync_batch_size', 50);
        $delete_batch_size = (int) get_option('avsight_delete_batch_size', 50);

        return '
            jQuery(document).ready(function($) {
                // Batch sizes from PHP settings
                var syncBatchSize = ' . $sync_batch_size . ';
                var deleteBatchSize = ' . $delete_batch_size . ';
                var isSyncRunning = false; // Global flag to control sync monitoring
                var isDeleteRunning = false; // Global flag to control delete monitoring
                var isBulkDeleteRunning = false; // Global flag for bulk delete AJAX loop
                var currentDeleteOffset = 0; // Track current delete offset

                // Inline log functions
                function addSyncLogEntry(level, message) {
                    var timestamp = new Date().toLocaleTimeString();
                    var logEntry = "<div class=\"avsight-log-entry\">" +
                        "<span class=\"avsight-log-timestamp\">[" + timestamp + "]</span> " +
                        "<span class=\"avsight-log-level-" + level + "\">" + message + "</span>" +
                        "</div>";
                    $("#avsight-sync-log").append(logEntry);
                    $("#avsight-sync-log").scrollTop($("#avsight-sync-log")[0].scrollHeight);
                }

                function addDeleteLogEntry(level, message) {
                    var timestamp = new Date().toLocaleTimeString();
                    var logEntry = "<div class=\"avsight-log-entry\">" +
                        "<span class=\"avsight-log-timestamp\">[" + timestamp + "]</span> " +
                        "<span class=\"avsight-log-level-" + level + "\">" + message + "</span>" +
                        "</div>";
                    $("#avsight-delete-log").append(logEntry);
                    $("#avsight-delete-log").scrollTop($("#avsight-delete-log")[0].scrollHeight);
                }

                // Log clear handlers
                $("#avsight-sync-log-clear").on("click", function() {
                    $("#avsight-sync-log").empty();
                });

                $("#avsight-delete-log-clear").on("click", function() {
                    $("#avsight-delete-log").empty();
                });

                // Handle sync form submission
                $("form").on("submit", function(e) {
                    var form = $(this);
                    if (form.find("input[name=\"avsight_sync_nonce\"]").length > 0) {
                        // This is the sync form
                        isSyncRunning = true;
                        setTimeout(function() {
                            if ($("#avsight-sync-log").length > 0) {
                                addSyncLogEntry("info", "Initiating inventory synchronization...");
                                monitorSyncProgress();
                            }
                        }, 2000); // Start monitoring after form submission
                    } else if (form.find("input[name=\"avsight_bulk_delete_nonce\"]").length > 0) {
                        // This is the delete form
                        isDeleteRunning = true;
                        isBulkDeleteRunning = true;
                        setTimeout(function() {
                            if ($("#avsight-delete-log").length > 0) {
                                addDeleteLogEntry("info", "Initiating bulk product deletion...");
                                // Start the actual deletion process
                                processDeleteBatch(0); // Start from offset 0
                                monitorDeleteProgress();
                            } else {
                                // If no log element, still start the process
                                processDeleteBatch(0);
                                monitorDeleteProgress();
                            }
                        }, 3000); // Give more time for page to fully load
                    }
                });

                // Add click handlers for stop buttons
                $("#avsight-stop-sync").on("click", function() {
                    if (confirm("Are you sure you want to stop the inventory sync process?\\n\\nThis will immediately stop all sync operations and reset the progress.")) {
                        // Immediately update UI to show stopping
                        isSyncRunning = false;
                        $("#avsight-stop-sync").prop("disabled", true).text("⏹️ Stopping...");
                        $("#avsight-sync-status-text").text("🛑 Stopping sync process...");

                        if ($("#avsight-sync-log").length > 0) {
                            addSyncLogEntry("warning", "🛑 Stopping sync process...");
                        }

                        $.post(ajaxurl, {
                            action: "avsight_stop_sync",
                            nonce: nonce
                        }, function(response) {
                            if (response.success) {
                                if ($("#avsight-sync-log").length > 0) {
                                    addSyncLogEntry("success", "✅ Sync stopped: " + response.data.message);
                                }
                                // Force immediate page refresh to reset all displays
                                setTimeout(function() {
                                    window.location.reload();
                                }, 1000);
                            } else {
                                if ($("#avsight-sync-log").length > 0) {
                                    addSyncLogEntry("error", "❌ Error stopping sync: " + (response.data ? response.data.message : "Unknown error"));
                                }
                                // Re-enable button on error
                                $("#avsight-stop-sync").prop("disabled", false).text("⏹️ Stop Sync");
                            }
                        }).fail(function(xhr, status, error) {
                            if ($("#avsight-sync-log").length > 0) {
                                addSyncLogEntry("error", "❌ Failed to stop sync: " + error);
                            }
                            // Re-enable button on failure
                            $("#avsight-stop-sync").prop("disabled", false).text("⏹️ Stop Sync");
                        });
                    }
                });

                $("#avsight-stop-delete").on("click", function() {
                    if (confirm("Are you sure you want to stop the bulk product deletion process?")) {
                        // Immediately stop all deletion processes
                        isBulkDeleteRunning = false;
                        isDeleteRunning = false;
                        
                        if ($("#avsight-delete-log").length > 0) {
                            addDeleteLogEntry("warning", "Stopping deletion process...");
                        }
                        
                        // Hide stop button immediately
                        $("#avsight-stop-delete").hide();
                        $("#avsight-delete-status-text").text("Stopping...");
                        
                        $.post(ajaxurl, {
                            action: "avsight_stop_bulk_delete",
                            nonce: nonce
                        }, function(response) {
                            if (response.success) {
                                if ($("#avsight-delete-log").length > 0) {
                                    addDeleteLogEntry("success", "Deletion stopped successfully.");
                                }
                                $("#avsight-delete-status-text").text("Deletion Stopped");
                            } else {
                                if ($("#avsight-delete-log").length > 0) {
                                    addDeleteLogEntry("error", "Error stopping deletion: " + (response.data && response.data.message ? response.data.message : "Unknown error."));
                                }
                            }
                        }).fail(function() {
                            if ($("#avsight-delete-log").length > 0) {
                                addDeleteLogEntry("error", "Failed to send stop deletion request to server.");
                            }
                        });
                    }
                });

                // Monitor sync progress with logging
                function monitorSyncProgress() {
                    if (!isSyncRunning) return;
                    
                    $.post(ajaxurl, {
                        action: "avsight_get_sync_status",
                        nonce: nonce
                    }, function(response) {
                        if (response.sync_status) {
                            var status = response.sync_status;
                            
                            // Update progress in log
                            if (status.total > 0 && $("#avsight-sync-log").length > 0) {
                                addSyncLogEntry("info", "Progress: " + status.processed + "/" + status.total + " (" + status.percentage + "%)");
                            }
                            
                            // Check for errors
                            if (response.error) {
                                if ($("#avsight-sync-log").length > 0) {
                                    addSyncLogEntry("error", "Sync error: " + response.error);
                                }
                                isSyncRunning = false;
                                return;
                            }
                            
                            // Check if completed
                            if (status.running === false && status.processed > 0) {
                                if ($("#avsight-sync-log").length > 0) {
                                    addSyncLogEntry("success", "Sync completed successfully! Processed " + status.processed + " items.");
                                }
                                isSyncRunning = false;
                                return;
                            }
                            
                            // Continue monitoring if still running
                            if (isSyncRunning) {
                                setTimeout(monitorSyncProgress, 3000);
                            }
                        }
                    }).fail(function() {
                        if ($("#avsight-sync-log").length > 0) {
                            addSyncLogEntry("error", "Failed to get sync status from server.");
                        }
                        isSyncRunning = false;
                    });
                }

                // Process delete batch function
                function processDeleteBatch(offset) {
                    // Check if deletion was stopped before starting batch
                    if (!isBulkDeleteRunning) {
                        return; // Don\'t log anything if stopped - user already knows
                    }

                    currentDeleteOffset = offset; // Update current offset

                    if ($("#avsight-delete-log").length > 0) {
                        addDeleteLogEntry("info", "Processing deletion batch starting at offset: " + offset);
                    }

                    $.post(ajaxurl, {
                        action: "avsight_ajax_process_delete_batch",
                        nonce: nonce,
                        offset: offset // Pass the current offset to the PHP handler
                    }, function(response) {
                        // Check if deletion was stopped while AJAX was in progress
                        if (!isBulkDeleteRunning) {
                            return; // Don\'t process response if stopped
                        }
                        
                        if (response.success) {
                            // Update UI with progress from this batch
                            $("#avsight-delete-processed").text(response.data.total_processed);
                            if (response.data.total_items > 0) {
                                var percentage = Math.round((response.data.total_processed / response.data.total_items) * 100);
                                $("#avsight-delete-percentage").text(percentage + "%");
                                $("#avsight-delete-progress-bar-inner").css("width", percentage + "%").text(percentage + "%");
                            }
                            
                            // Log progress only if still running
                            if (isBulkDeleteRunning && $("#avsight-delete-log").length > 0) {
                                addDeleteLogEntry("info", "Batch completed: " + response.data.items_in_batch_processed + " products deleted. Total: " + response.data.total_processed + "/" + response.data.total_items);
                            }

                            if (response.data.completed) {
                                isBulkDeleteRunning = false;
                                $("#avsight-delete-status-text").text("Deletion Completed");
                                $("#avsight-stop-delete").hide();
                                if ($("#avsight-delete-log").length > 0) {
                                    addDeleteLogEntry("success", "Bulk product deletion completed successfully! Total deleted: " + response.data.total_processed);
                                }
                                updateSyncStatus(); // Final status update
                            } else if (isBulkDeleteRunning) {
                                // Schedule next batch with a small delay, but check again if still running
                                setTimeout(function() {
                                    if (isBulkDeleteRunning) { // Double-check before next batch
                                        processDeleteBatch(response.data.next_offset);
                                    }
                                }, 1000); // 1 second delay between batches
                            }
                        } else {
                            // Only log errors if deletion wasn\'t intentionally stopped
                            var errorMsg = response.data && response.data.message ? response.data.message : "Unknown error.";
                            if (isBulkDeleteRunning && !errorMsg.includes("file not found") && !errorMsg.includes("stopped")) {
                                if ($("#avsight-delete-log").length > 0) {
                                    addDeleteLogEntry("error", "Error during bulk deletion batch: " + errorMsg);
                                }
                                $("#avsight-delete-status-text").text("Deletion Error");
                            }
                            isBulkDeleteRunning = false;
                            $("#avsight-stop-delete").hide();
                            updateSyncStatus(); // Refresh status to show error state
                        }
                    }).fail(function(jqXHR, textStatus, errorThrown) {
                        // Only log AJAX failures if deletion wasn\'t intentionally stopped
                        if (isBulkDeleteRunning) {
                            var errorMsg = "AJAX call failed for bulk deletion: " + textStatus + " - " + errorThrown;
                            if ($("#avsight-delete-log").length > 0) {
                                addDeleteLogEntry("error", errorMsg);
                            }
                            $("#avsight-delete-status-text").text("AJAX Error");
                        }
                        isBulkDeleteRunning = false;
                        $("#avsight-stop-delete").hide();
                        updateSyncStatus();
                    });
                }

                // Monitor delete progress with logging
                function monitorDeleteProgress() {
                    if (!isBulkDeleteRunning) return;
                    
                    $.post(ajaxurl, {
                        action: "avsight_get_sync_status",
                        nonce: nonce
                    }, function(response) {
                        if (response.delete_status) {
                            var status = response.delete_status;
                            
                            // Update progress in log
                            if (status.total > 0 && $("#avsight-delete-log").length > 0) {
                                addDeleteLogEntry("info", "Deletion progress: " + status.processed + "/" + status.total + " (" + status.percentage + "%)");
                            }
                            
                            // Check for errors
                            if (response.delete_error) {
                                if ($("#avsight-delete-log").length > 0) {
                                    addDeleteLogEntry("error", "Deletion error: " + response.delete_error);
                                }
                                isBulkDeleteRunning = false;
                                return;
                            }
                            
                            // Check if completed
                            if (status.running === false && status.processed > 0) {
                                if ($("#avsight-delete-log").length > 0) {
                                    addDeleteLogEntry("success", "Deletion completed successfully! Deleted " + status.processed + " products.");
                                }
                                isBulkDeleteRunning = false;
                                return;
                            }
                            
                            // Continue monitoring if still running
                            if (isBulkDeleteRunning) {
                                setTimeout(monitorDeleteProgress, 3000);
                            }
                        }
                    }).fail(function() {
                        if ($("#avsight-delete-log").length > 0) {
                            addDeleteLogEntry("error", "Failed to get deletion status from server.");
                        }
                        isBulkDeleteRunning = false;
                    });
                }

                // Update sync status function
                function updateSyncStatus() {
                    $.post(ajaxurl, {
                        action: "avsight_get_sync_status",
                        nonce: nonce
                    }, function(response) {
                        if (response.sync_status) {
                            var status = response.sync_status;

                            // Update live log if available
                            if (status.log && status.log.length > 0 && $("#avsight-sync-log").length > 0) {
                                $("#avsight-sync-log").empty(); // Clear existing log
                                status.log.forEach(function(logEntry) {
                                    addSyncLogEntry(logEntry.level, logEntry.message);
                                });
                            }
                            
                            // Always update progress elements (even when sync is completed)
                            $("#avsight-sync-total").text(status.total);
                            $("#avsight-sync-processed").text(status.processed);
                            $("#avsight-sync-percentage").text(status.percentage + "%");
                            $("#avsight-sync-progress-bar-inner").css("width", status.percentage + "%").text(status.percentage + "%");
                            $("#avsight-sync-time-elapsed").text(formatTime(status.time_elapsed));
                            $("#avsight-sync-time-remaining").text(status.time_remaining);

                            // UPDATE MAIN STATUS ELEMENTS (the ones actually visible on the page)
                            if (status.total > 0 || status.processed > 0) {
                                // We have processing data - update main status elements
                                $("#avsight-main-status").html("<strong>Status:</strong> <span style=\"color: #0073aa;\">🔄 Processing products...</span>");
                                $("#avsight-main-progress").html("<strong>Progress:</strong> <span style=\"color: #666;\">" + status.processed + "/" + status.total + " (" + status.percentage + "%)</span>");

                                // Update ALL progress bars (including the main one)
                                $(".avsight-progress-bar-inner").css("width", status.percentage + "%").html(status.percentage + "%");
                            }

                            if (status.running) {
                                if (status.bulk_query_monitoring) {
                                    $("#avsight-sync-status-text").text("🔄 Sync In Progress (Waiting for Salesforce)");
                                } else {
                                    $("#avsight-sync-status-text").text("🔄 Sync In Progress");
                                }
                                $("#avsight-stop-sync").show();

                                // Show current batch status for sync (only if we have totals)
                                if (status.total > 0) {
                                    $("#avsight-sync-current-batch").text("Batch " + Math.floor(status.processed / syncBatchSize) + " of " + Math.ceil(status.total / syncBatchSize));
                                } else {
                                    $("#avsight-sync-current-batch").text("Preparing...");
                                }
                            } else {
                                $("#avsight-sync-status-text").text("✅ No sync currently running");
                                $("#avsight-stop-sync").hide();

                                // UPDATE MAIN STATUS FOR COMPLETION
                                if (status.total > 0 && status.processed >= status.total) {
                                    $("#avsight-main-status").html("<strong>Status:</strong> <span style=\"color: green;\">✅ Sync completed successfully!</span>");
                                    $("#avsight-main-progress").html("<strong>Progress:</strong> <span style=\"color: green;\">Completed - " + status.processed + "/" + status.total + " items processed</span>");
                                    $(".avsight-progress-bar-inner").css("width", "100%").html("100%");
                                }

                                // Show final batch status when completed
                                if (status.total > 0) {
                                    $("#avsight-sync-current-batch").text("Completed - " + Math.ceil(status.total / syncBatchSize) + " batches processed");
                                } else {
                                    $("#avsight-sync-current-batch").text("N/A");
                                }
                            }
                        }

                        if (response.delete_status) {
                            var status = response.delete_status;
                            
                            if (status.running) {
                                $("#avsight-delete-status-text").text("Deletion In Progress");
                                $("#avsight-stop-delete").show();
                                // Only resume if we haven\'t explicitly stopped the process
                                if (!isBulkDeleteRunning) { // If status says running, but JS loop isn\'t, start it.
                                    console.log("Bulk delete is running on server, starting/resuming client-side AJAX loop.");
                                    isBulkDeleteRunning = true;
                                    if ($("#avsight-delete-log").length > 0) {
                                        addDeleteLogEntry("info", "Resuming deletion process from offset: " + status.processed);
                                    }
                                    // The PHP handler uses get_transient to get the current offset
                                    processDeleteBatch(status.processed);
                                }
                            } else { // Not running according to server status
                                isBulkDeleteRunning = false;
                                $("#avsight-delete-status-text").text("✅ No deletion currently running");
                                $("#avsight-stop-delete").hide();
                            }

                            $("#avsight-delete-total").text(status.total);
                            $("#avsight-delete-processed").text(status.processed);
                            $("#avsight-delete-percentage").text(status.percentage + "%");
                            $("#avsight-delete-progress-bar-inner").css("width", status.percentage + "%").text(status.percentage + "%");
                            $("#avsight-delete-time-elapsed").text(formatTime(status.time_elapsed));
                            $("#avsight-delete-time-remaining").text(status.time_remaining);
                            // Show current batch status
                            $("#avsight-delete-current-batch").text("Batch " + Math.floor(status.processed / deleteBatchSize) + " of " + Math.ceil(status.total / deleteBatchSize));
                        }

                        // Stop status updates if both sync and delete are completed
                        var syncRunning = response.sync_status && response.sync_status.running;
                        var deleteRunning = response.delete_status && response.delete_status.running;

                        if (!syncRunning && !deleteRunning) {
                            console.log("Both sync and delete completed, stopping status updates");
                            clearInterval(statusUpdateInterval);
                        }
                    });
                }

                // Format time function
                function formatTime(seconds) {
                    if (seconds <= 0) return "00:00:00";
                    var hours = Math.floor(seconds / 3600);
                    var minutes = Math.floor((seconds % 3600) / 60);
                    var secs = Math.floor(seconds % 60);
                    return String(hours).padStart(2, "0") + ":" + String(minutes).padStart(2, "0") + ":" + String(secs).padStart(2, "0");
                }

                // Auto-update status every 5 seconds
                var statusUpdateInterval = setInterval(updateSyncStatus, 5000);

                // Initial status update
                updateSyncStatus();
            });
        ';
    }

    /**
     * Show admin notices
     */
    public static function showAdminNotices()
    {
        // Check if we're on the Avsight sync page
        $screen = get_current_screen();
        if ($screen && $screen->id !== 'toplevel_page_avsight-sync') {
            return;
        }

        // Show configuration warnings if needed
        $config_validation = AvsightApi::validateConfig();
        if (is_wp_error($config_validation)) {
            echo '<div class="notice notice-warning"><p><strong>Avsight Sync:</strong> ' . esc_html($config_validation->get_error_message()) . '</p></div>';
        }
    }
}
