<?php

namespace App\AvsightSync;

// Import required classes
require_once __DIR__ . '/AvsightProductMerger.php';
require_once __DIR__ . '/AvsightApi.php';

/**
 * Handles inventory synchronization operations with product merging capabilities
 */
class AvsightSyncOperations
{
    /**
     * Initiates the inventory synchronization process
     */
    public static function runInventorySync()
    {
        // Determine sync type for logging
        $last_sync = get_option('avsight_last_successful_sync', false);
        $incremental_enabled = get_option('avsight_incremental_sync_enabled', false);

        // Debug logging for sync type determination
        error_log('Avsight Sync Debug: Last sync: ' . ($last_sync ?: 'NONE'));
        error_log('Avsight Sync Debug: Incremental enabled: ' . ($incremental_enabled ? 'YES' : 'NO'));

        $sync_type = ($last_sync && $incremental_enabled) ? 'incremental' : 'full';

        error_log('Avsight Sync: Starting ' . $sync_type . ' inventory synchronization.');
        self::clearSyncLog();
        self::addSyncLogMessage('🚀 Starting ' . $sync_type . ' inventory synchronization...');

        if ($sync_type === 'incremental') {
            $last_sync_formatted = date('Y-m-d H:i:s', strtotime($last_sync));
            self::addSyncLogMessage('📅 Last sync: ' . $last_sync_formatted . ' - only syncing changed products');
        } else {
            self::addSyncLogMessage('📦 Full sync - processing all products');
        }

        // Add detailed debug logging
        error_log('Avsight Sync Debug: About to call AvsightApi::initiateBulkQuery()');
        self::addSyncLogMessage('🔗 Connecting to Salesforce API...');

        // Clear any existing scheduled batches
        wp_clear_scheduled_hook('avsight_process_inventory_batch_event');

        // Clear old transients and temp files
        delete_transient(AVSIGHT_SYNC_TRANSIENT_KEY);
        self::deleteTempFileAndTransient(AVSIGHT_SYNC_DATA_FILE_PATH_KEY, AVSIGHT_SYNC_DATA_FILE_OFFSET_KEY);
        self::deleteTempFileAndTransient(AVSIGHT_ALL_PRODUCT_IDS_FILE_PATH_KEY);
        self::deleteTempFileAndTransient(AVSIGHT_SYNCED_PRODUCT_IDS_FILE_PATH_KEY);
        self::deleteTempFileAndTransient(AVSIGHT_UNSYNCED_IDS_FILE_PATH_KEY, AVSIGHT_UNSYNCED_IDS_FILE_OFFSET_KEY);

        // Clear ALL old sync data to start fresh
        delete_transient('avsight_synced_product_ids');
        delete_transient('avsight_all_product_ids');
        delete_transient(AVSIGHT_SYNC_TOTAL_ITEMS_KEY);
        delete_transient(AVSIGHT_SYNC_PROCESSED_ITEMS_KEY);
        delete_transient(AVSIGHT_SYNC_DATA_FILE_OFFSET_KEY);
        delete_transient(AVSIGHT_SYNC_LAST_RUN_END_KEY);
        delete_transient('avsight_sync_error');
        delete_transient('avsight_sync_log_messages');

        // Set start time and running status
        set_transient(AVSIGHT_SYNC_TRANSIENT_KEY, true, HOUR_IN_SECONDS);
        set_transient(AVSIGHT_SYNC_LAST_RUN_START_KEY, time());

        // Initialize with waiting status (no items yet - waiting for Salesforce)
        set_transient(AVSIGHT_SYNC_TOTAL_ITEMS_KEY, 0);
        set_transient(AVSIGHT_SYNC_PROCESSED_ITEMS_KEY, 0);

        // Get Salesforce access token
        $access_token = AvsightApi::getAccessToken();
        if (is_wp_error($access_token)) {
            $error_message = 'Failed to get Salesforce access token - ' . $access_token->get_error_message();
            error_log('Avsight Sync Error: ' . $error_message);
            set_transient('avsight_sync_error', $error_message, HOUR_IN_SECONDS);
            set_transient(AVSIGHT_SYNC_LAST_RUN_END_KEY, time());
            delete_transient(AVSIGHT_SYNC_TRANSIENT_KEY);
            return;
        }

        // Initiate bulk query
        error_log('Avsight Sync Debug: Calling AvsightApi::initiateBulkQuery() with access token');
        $job_id = AvsightApi::initiateBulkQuery($access_token);

        error_log('Avsight Sync Debug: initiateBulkQuery() returned: ' . (is_wp_error($job_id) ? 'WP_Error: ' . $job_id->get_error_message() : 'Job ID: ' . $job_id));

        if (is_wp_error($job_id)) {
            $error_message = 'Failed to initiate bulk query - ' . $job_id->get_error_message();
            error_log('Avsight Sync Error: ' . $error_message);
            self::addSyncLogMessage('❌ ' . $error_message);
            set_transient('avsight_sync_error', $error_message, HOUR_IN_SECONDS);
            set_transient(AVSIGHT_SYNC_LAST_RUN_END_KEY, time());
            delete_transient(AVSIGHT_SYNC_TRANSIENT_KEY);
            return;
        }

        self::addSyncLogMessage('✅ Bulk query job created: ' . $job_id);

        error_log('Avsight Sync: Bulk query job initiated with ID: ' . $job_id);
        self::addSyncLogMessage('📊 Bulk query job initiated with ID: ' . $job_id);
        self::addSyncLogMessage('⏳ Waiting for Salesforce to process bulk query...');

        // Store job ID and access token for background processing
        set_transient('avsight_bulk_query_job_id', $job_id, HOUR_IN_SECONDS);
        set_transient('avsight_bulk_query_access_token', $access_token, HOUR_IN_SECONDS);

        // Schedule background job monitoring
        wp_schedule_single_event(time() + 10, 'avsight_monitor_bulk_query_event');

        error_log('Avsight Sync: Background monitoring scheduled for bulk query job: ' . $job_id);
        self::addSyncLogMessage('🔄 Background processing initiated - sync will continue automatically');
    }

    /**
     * Monitor bulk query job status (background cron job)
     */
    public static function monitorBulkQuery()
    {
        error_log('Avsight Sync: monitorBulkQuery() called');

        // Get stored job details
        $job_id = get_transient('avsight_bulk_query_job_id');
        $access_token = get_transient('avsight_bulk_query_access_token');

        if (!$job_id || !$access_token) {
            error_log('Avsight Sync: No bulk query job to monitor');
            return;
        }

        // Check job status
        $job_status = AvsightApi::getBulkQueryJobStatus($access_token, $job_id);
        if (is_wp_error($job_status)) {
            error_log('Avsight Sync: Error checking job status: ' . $job_status->get_error_message());
            // Retry in 10 seconds
            wp_schedule_single_event(time() + 10, 'avsight_monitor_bulk_query_event');
            return;
        }

        error_log('Avsight Sync: Job status: ' . $job_status['state']);
        self::addSyncLogMessage('📊 Bulk query status: ' . $job_status['state']);

        if ($job_status['state'] === 'JobComplete') {
            // Job completed, get results
            $results = AvsightApi::getBulkQueryResults($access_token, $job_id);
            if (!is_wp_error($results)) {
                error_log('Avsight Sync: Bulk query completed. Processing results...');
                self::addSyncLogMessage('✅ Bulk query completed, processing results...');

                // Clean up transients
                delete_transient('avsight_bulk_query_job_id');
                delete_transient('avsight_bulk_query_access_token');

                // Process results
                self::processBulkQueryResults($results);
            } else {
                error_log('Avsight Sync: Error getting results: ' . $results->get_error_message());
                self::addSyncLogMessage('❌ Error getting bulk query results: ' . $results->get_error_message());
                set_transient('avsight_sync_error', 'Error getting bulk query results: ' . $results->get_error_message(), HOUR_IN_SECONDS);
                set_transient(AVSIGHT_SYNC_LAST_RUN_END_KEY, time());
                delete_transient(AVSIGHT_SYNC_TRANSIENT_KEY);
            }
        } elseif ($job_status['state'] === 'Failed') {
            $error_message = 'Bulk query job failed: ' . ($job_status['stateMessage'] ?? 'Unknown error');
            error_log('Avsight Sync Error: ' . $error_message);
            self::addSyncLogMessage('❌ ' . $error_message);
            set_transient('avsight_sync_error', $error_message, HOUR_IN_SECONDS);
            set_transient(AVSIGHT_SYNC_LAST_RUN_END_KEY, time());
            delete_transient(AVSIGHT_SYNC_TRANSIENT_KEY);

            // Clean up transients
            delete_transient('avsight_bulk_query_job_id');
            delete_transient('avsight_bulk_query_access_token');
        } else {
            // Job still in progress, check again in 10 seconds
            wp_schedule_single_event(time() + 10, 'avsight_monitor_bulk_query_event');
        }
    }

    /**
     * Processes bulk query results and initiates batch processing
     *
     * @param array $results Bulk query results from Salesforce
     */
    private static function processBulkQueryResults($results)
    {
        error_log('Avsight Sync Debug: Processing bulk query results. Type: ' . gettype($results) . ', Count: ' . (is_array($results) ? count($results) : 'N/A'));
        error_log('Avsight Sync Debug: First few results: ' . json_encode(array_slice($results, 0, 3)));

        if (empty($results) || !is_array($results)) {
            // Check if this is an incremental sync with no changes
            $last_sync = get_option('avsight_last_successful_sync', false);
            $incremental_enabled = get_option('avsight_incremental_sync_enabled', false);

            // Debug logging for empty results
            error_log('Avsight Sync Debug: Empty results - Last sync: ' . ($last_sync ?: 'NONE'));
            error_log('Avsight Sync Debug: Empty results - Incremental enabled: ' . ($incremental_enabled ? 'YES' : 'NO'));

            if ($last_sync && $incremental_enabled) {
                // This is an incremental sync with no changes - this is normal
                error_log('Avsight Sync: Incremental sync completed - no changes found since last sync');
                self::addSyncLogMessage('✅ Incremental sync completed - no changes found since last sync');
                self::addSyncLogMessage('📅 Last sync: ' . date('Y-m-d H:i:s', strtotime($last_sync)));

                // For incremental syncs with no changes, DO NOT run product removal
                // because we didn't get the full product list from Avsight
                self::addSyncLogMessage('ℹ️ Skipping product removal check - incremental sync with no changes');

                // Update last sync timestamp
                update_option('avsight_last_successful_sync', gmdate('Y-m-d\TH:i:s\Z'));
                set_transient(AVSIGHT_SYNC_LAST_RUN_END_KEY, time());
                delete_transient(AVSIGHT_SYNC_TRANSIENT_KEY);
                return;
            } else {
                // This is a full sync with no results - this is an error
                error_log('Avsight Sync Error: Empty or invalid bulk query results. Results: ' . json_encode($results));
                set_transient('avsight_sync_error', 'Empty or invalid bulk query results', HOUR_IN_SECONDS);
                set_transient(AVSIGHT_SYNC_LAST_RUN_END_KEY, time());
                return;
            }
        }

        // Group and merge products by Part Number + Warehouse + Condition + Certifications
        error_log('Avsight Sync: Starting product grouping and merging...');
        self::addSyncLogMessage('🔄 Grouping and merging duplicate products...');

        $merged_products = AvsightProductMerger::groupAndMergeProducts($results);

        error_log('Avsight Sync: Product merging completed. Original items: ' . count($results) . ', Merged groups: ' . count($merged_products));
        self::addSyncLogMessage('✅ Product merging completed: ' . count($results) . ' items → ' . count($merged_products) . ' unique products');

        // Write merged results to temporary file (use JSON lines format)
        $data_file_path = self::writeDataToFile(AVSIGHT_SYNC_DATA_FILE_PATH_KEY, $merged_products, true);

        // Track current sync products for removal handling (use merged products for accurate tracking)
        self::trackCurrentSyncProducts($merged_products, true);
        if (!$data_file_path) {
            error_log('Avsight Sync Error: Failed to write sync data to temporary file.');
            set_transient('avsight_sync_error', 'Failed to write sync data to temporary file', HOUR_IN_SECONDS);
            set_transient(AVSIGHT_SYNC_LAST_RUN_END_KEY, time());
            return;
        }

        // Set up batch processing transients - RESET ALL COUNTERS
        delete_transient(AVSIGHT_SYNC_DATA_FILE_OFFSET_KEY);
        delete_transient(AVSIGHT_SYNC_TOTAL_ITEMS_KEY);
        delete_transient(AVSIGHT_SYNC_PROCESSED_ITEMS_KEY);

        set_transient(AVSIGHT_SYNC_DATA_FILE_OFFSET_KEY, 0, DAY_IN_SECONDS);
        set_transient(AVSIGHT_SYNC_TOTAL_ITEMS_KEY, count($merged_products));
        set_transient(AVSIGHT_SYNC_PROCESSED_ITEMS_KEY, 0);

        error_log('Avsight Sync: Bulk query results processed. Original items: ' . count($results) . ', Merged products: ' . count($merged_products));
        error_log('Avsight Sync: Starting batch processing...');
        self::addSyncLogMessage('✅ Retrieved ' . count($results) . ' inventory items from Avsight');
        self::addSyncLogMessage('🔄 Starting batch processing...');

        // Schedule first batch
        $scheduled = wp_schedule_single_event(time() + 5, 'avsight_process_inventory_batch_event');
        error_log('Avsight Sync: Cron event scheduled: ' . ($scheduled ? 'SUCCESS' : 'FAILED'));

        // Also trigger immediately as backup (in case cron is disabled)
        error_log('Avsight Sync: Triggering first batch immediately as backup...');
        self::processInventoryBatch();
    }

    /**
     * Processes a single batch of inventory items
     */
    public static function processInventoryBatch()
    {
        error_log('Avsight Sync: processInventoryBatch() called');

        $data_file_path = get_transient(AVSIGHT_SYNC_DATA_FILE_PATH_KEY);
        $current_offset = (int) get_transient(AVSIGHT_SYNC_DATA_FILE_OFFSET_KEY);
        $total_items = (int) get_transient(AVSIGHT_SYNC_TOTAL_ITEMS_KEY);
        $processed_items = (int) get_transient(AVSIGHT_SYNC_PROCESSED_ITEMS_KEY);

        error_log('Avsight Sync Debug: Batch processing - File: ' . ($data_file_path ?: 'NOT SET'));
        error_log('Avsight Sync Debug: Batch processing - Offset: ' . $current_offset);
        error_log('Avsight Sync Debug: Batch processing - Total: ' . $total_items);
        error_log('Avsight Sync Debug: Batch processing - Processed: ' . $processed_items);

        if (!$data_file_path || !file_exists($data_file_path)) {
            error_log('Avsight Sync Error: Data file not found. Batch processing stopped.');
            set_transient('avsight_sync_error', 'Data file not found during batch processing', HOUR_IN_SECONDS);
            set_transient(AVSIGHT_SYNC_LAST_RUN_END_KEY, time());
            return;
        }

        // Read batch from file using configurable batch size
        $sync_batch_size = get_option('avsight_sync_batch_size', AVSIGHT_SYNC_BATCH_SIZE);
        $file_read_result = self::readLinesFromFile($data_file_path, $current_offset, $sync_batch_size);
        $batch_items = $file_read_result['items'];
        $next_offset = $file_read_result['next_line_to_process'];

        if (empty($batch_items)) {
            error_log('Avsight Sync: No more items to process. Sync completed.');
            self::completeSyncProcess();
            return;
        }

        // Process each item in the batch
        $batch_processed = 0;
        $batch_failed = 0;
        $batch_created = 0;
        $batch_updated = 0;

        foreach ($batch_items as $item) {
            $result = self::updateWooCommerceProduct($item);
            if ($result === 'created') {
                $batch_processed++;
                $batch_created++;
            } elseif ($result === 'updated') {
                $batch_processed++;
                $batch_updated++;
            } else {
                $batch_failed++;
            }
        }

        // Update progress
        $new_processed_count = $processed_items + $batch_processed;
        set_transient(AVSIGHT_SYNC_PROCESSED_ITEMS_KEY, $new_processed_count);
        set_transient(AVSIGHT_SYNC_DATA_FILE_OFFSET_KEY, $next_offset, DAY_IN_SECONDS);

        error_log('Avsight Sync: Batch processed. Success: ' . $batch_processed . ', Failed: ' . $batch_failed . ', Created: ' . $batch_created . ', Updated: ' . $batch_updated . '. Total progress: ' . $new_processed_count . '/' . $total_items);

        $log_message = '📦 Processed batch: ' . $batch_processed . '/' . count($batch_items) . ' items';
        if ($batch_failed > 0) {
            $log_message .= ' (⚠️ ' . $batch_failed . ' failed)';
        }
        if ($batch_created > 0) {
            $log_message .= ' (🆕 ' . $batch_created . ' created)';
        }
        if ($batch_updated > 0) {
            $log_message .= ' (🔄 ' . $batch_updated . ' updated)';
        }
        $log_message .= '. Total: ' . $new_processed_count . '/' . $total_items . ' (' . round(($new_processed_count / $total_items) * 100) . '%)';

        self::addSyncLogMessage($log_message);

        // Schedule next batch if not completed
        if ($next_offset < $total_items && $new_processed_count < $total_items) {
            wp_schedule_single_event(time() + AVSIGHT_SYNC_BATCH_INTERVAL, 'avsight_process_inventory_batch_event');
        } else {
            self::completeSyncProcess();
        }
    }

    /**
     * Updates a WooCommerce product with merged Avsight inventory data
     *
     * @param array $merged_product Merged product group from AvsightProductMerger
     * @return string|false 'created', 'updated', or false on failure
     */
    private static function updateWooCommerceProduct($merged_product)
    {
        // Handle merged product structure
        if (isset($merged_product['part_number']) && isset($merged_product['total_quantity'])) {
            // New merged product format
            $product_name = AvsightProductMerger::generateProductTitle($merged_product);
            $sku = AvsightProductMerger::generateSku($merged_product);
            $quantity = (int) $merged_product['total_quantity'];
            $cost = (float) $merged_product['average_cost'];

            error_log('Avsight Sync Debug: Processing merged product: ' . $product_name . ' (SKU: ' . $sku . ', quantity: ' . $quantity . ', cost: ' . $cost . ')');
        } else {
            // Fallback for old format (shouldn't happen with new merger)
            error_log('Avsight Sync Warning: Invalid merged product structure: ' . json_encode($merged_product));
            return false;
        }

        // Check if this is a duplicate within the same sync
        static $processed_products = [];
        if (isset($processed_products[$sku])) {
            error_log('Avsight Sync Warning: DUPLICATE product SKU "' . $sku . '" found in same sync! Previous result: ' . $processed_products[$sku]);
        }

        // Find WooCommerce product by SKU first, then by title
        $product_id = wc_get_product_id_by_sku($sku);
        error_log('Avsight Sync Debug: SKU search for "' . $sku . '" result: ' . ($product_id ?: 'NOT FOUND'));

        // Debug: Check total WooCommerce products
        $total_wc_products = wp_count_posts('product');
        error_log('Avsight Sync Debug: Total WooCommerce products in database: ' . $total_wc_products->publish);

        if (!$product_id) {
            // Try to find by title if SKU doesn't match
            error_log('Avsight Sync Debug: Trying title search for "' . $product_name . '"');
            $products = get_posts(array(
                'post_type' => 'product',
                'title' => $product_name,
                'posts_per_page' => 1,
                'fields' => 'ids'
            ));

            if (!empty($products)) {
                $product_id = $products[0];
                error_log('Avsight Sync Debug: Title search found product ID: ' . $product_id);
            } else {
                error_log('Avsight Sync Debug: Title search for "' . $product_name . '" result: NOT FOUND');
            }
        }

        if (!$product_id) {
            // Product not found, create new product
            error_log('Avsight Sync Debug: Product "' . $product_name . '" (SKU: ' . $sku . ') not found in WooCommerce, creating new product');
            $product_id = self::createWooCommerceProduct($merged_product);
            if (!$product_id) {
                error_log('Avsight Sync Error: Failed to create product "' . $product_name . '"');
                return false;
            }
            error_log('Avsight Sync Success: Created new product "' . $product_name . '" (ID: ' . $product_id . ') with stock ' . $quantity);
            $processed_products[$sku] = 'created';
            return 'created';
        }

        // Update existing product with merged data
        error_log('Avsight Sync Debug: Found product ID ' . $product_id . ' for "' . $product_name . '", updating with merged data');
        $update_result = self::updateExistingWooCommerceProduct($product_id, $merged_product);

        if (!$update_result) {
            error_log('Avsight Sync Error: Failed to update product ID ' . $product_id);
            return false;
        }

        error_log('Avsight Sync Success: Updated product "' . $product_name . '" (ID: ' . $product_id . ') with merged data');
        $processed_products[$sku] = 'updated';
        return 'updated';
    }

    /**
     * Creates a new WooCommerce product with merged Avsight inventory data
     *
     * @param array $merged_product Merged product group from AvsightProductMerger
     * @return int|false Product ID on success, false on failure
     */
    private static function createWooCommerceProduct($merged_product)
    {
        try {
            // Extract data from merged product
            $product_name = AvsightProductMerger::generateProductTitle($merged_product);
            $sku = AvsightProductMerger::generateSku($merged_product);
            $quantity = (int) $merged_product['total_quantity'];
            $cost = (float) $merged_product['average_cost'];

            // Create new WooCommerce product
            $product = new \WC_Product_Simple();

            // Set basic product data
            $product->set_name($product_name);
            $product->set_sku($sku);
            $product->set_status('publish');
            $product->set_catalog_visibility('visible');

            // Set stock data
            $product->set_manage_stock(true);
            $product->set_stock_quantity($quantity);
            $product->set_stock_status($quantity > 0 ? 'instock' : 'outofstock');

            // Set pricing if cost is available
            if ($cost > 0) {
                $product->set_regular_price($cost);
            }

            // Set basic product description (clean, no technical data)
            $clean_description = self::generateCleanProductDescription($merged_product);
            $product->set_description($clean_description);

            // Set short description with key info (use display condition name)
            $condition_mapping = AvsightApi::getConditionDisplayMapping();
            $raw_condition = $merged_product['condition'];
            $display_condition = $condition_mapping[$raw_condition] ?? $raw_condition;

            $short_desc = 'Part: ' . $merged_product['part_number'] . ' | Condition: ' . $display_condition . ' | Warehouse: ' . $merged_product['warehouse'];
            if (!empty($merged_product['certifications'])) {
                $short_desc .= ' | Owner: ' . $merged_product['certifications'];
            }
            $product->set_short_description($short_desc);

            // Save the product
            $product_id = $product->save();

            // Add Avsight metadata to track this product
            if ($product_id) {
                self::updateProductMetadata($product_id, $merged_product);

                // Assign WooCommerce product attributes for filtering
                self::assignProductAttributes($product_id, $merged_product);
            }

            if ($product_id) {
                return $product_id;
            } else {
                error_log('Avsight Sync Error: Failed to save new product "' . $product_name . '"');
                return false;
            }
        } catch (\Exception $e) {
            error_log('Avsight Sync Error: Exception creating product "' . ($product_name ?? 'unknown') . '": ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Updates an existing WooCommerce product with merged Avsight inventory data
     *
     * @param int $product_id WooCommerce product ID
     * @param array $merged_product Merged product group from AvsightProductMerger
     * @return bool True on success, false on failure
     */
    private static function updateExistingWooCommerceProduct($product_id, $merged_product)
    {
        try {
            $product = wc_get_product($product_id);
            if (!$product) {
                error_log('Avsight Sync Error: Could not load product ID ' . $product_id);
                return false;
            }

            $quantity = (int) $merged_product['total_quantity'];
            $cost = (float) $merged_product['average_cost'];

            // Update stock data
            $product->set_stock_quantity($quantity);
            $product->set_manage_stock(true);
            $product->set_stock_status($quantity > 0 ? 'instock' : 'outofstock');

            // Update pricing if cost is available
            if ($cost > 0) {
                $product->set_regular_price($cost);
            }

            // Update product description (clean, no technical data)
            $clean_description = self::generateCleanProductDescription($merged_product);
            $product->set_description($clean_description);

            // Update short description with key info (use display condition name)
            $condition_mapping = AvsightApi::getConditionDisplayMapping();
            $raw_condition = $merged_product['condition'];
            $display_condition = $condition_mapping[$raw_condition] ?? $raw_condition;

            $short_desc = 'Part: ' . $merged_product['part_number'] . ' | Condition: ' . $display_condition . ' | Warehouse: ' . $merged_product['warehouse'];
            if (!empty($merged_product['certifications'])) {
                $short_desc .= ' | Owner: ' . $merged_product['certifications'];
            }
            $product->set_short_description($short_desc);

            // Save the product
            $result = $product->save();

            if ($result) {
                // Update Avsight metadata
                self::updateProductMetadata($product_id, $merged_product);

                // Update WooCommerce product attributes for filtering
                self::assignProductAttributes($product_id, $merged_product);
                return true;
            } else {
                error_log('Avsight Sync Error: Failed to save updated product ID ' . $product_id);
                return false;
            }
        } catch (\Exception $e) {
            error_log('Avsight Sync Error: Exception updating product ID ' . $product_id . ': ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Generate clean product description for WordPress content (no technical data)
     *
     * @param array $merged_product Merged product group
     * @return string Clean description for frontend display
     */
    private static function generateCleanProductDescription($merged_product)
    {
        $description_parts = [];

        // Add the main product description from Avsight
        if (!empty($merged_product['description'])) {
            $description_parts[] = esc_html($merged_product['description']);
        }

        // Add basic product information
        $description_parts[] = '';
        $description_parts[] = '<strong>Part Number:</strong> ' . esc_html($merged_product['part_number']);

        if (!empty($merged_product['certifications'])) {
            $description_parts[] = '<strong>Owner/Certifications:</strong> ' . esc_html($merged_product['certifications']);
        }

        // Note about availability
        $description_parts[] = '';
        $description_parts[] = '<em>This product is available from our Avsight inventory system. All technical details and specifications are managed through our inventory fields.</em>';

        return implode("\n", $description_parts);
    }

    /**
     * Update product metadata and ACF fields with merged product information
     *
     * @param int $product_id WooCommerce product ID
     * @param array $merged_product Merged product group
     */
    private static function updateProductMetadata($product_id, $merged_product)
    {
        // Standard Avsight metadata (for system use)
        update_post_meta($product_id, '_avsight_synced', 'yes');
        update_post_meta($product_id, '_avsight_last_sync', time());

        // Store the product title for legacy compatibility and the SKU for new tracking
        $product_title = AvsightProductMerger::generateProductTitle($merged_product);
        $product_sku = AvsightProductMerger::generateSku($merged_product);
        update_post_meta($product_id, '_avsight_product_name', $product_title);
        update_post_meta($product_id, '_avsight_product_sku', $product_sku);

        // ACF Fields - Product Information Tab
        update_field('avsight_part_number', $merged_product['part_number'], $product_id);
        update_field('avsight_description', $merged_product['description'] ?? '', $product_id);

        // Use display condition name from ACF configuration
        $condition_mapping = AvsightApi::getConditionDisplayMapping();
        $raw_condition = $merged_product['condition'];
        $display_condition = $condition_mapping[$raw_condition] ?? $raw_condition;
        update_field('avsight_condition', $display_condition, $product_id);

        update_field('avsight_warehouse', $merged_product['warehouse'], $product_id);
        update_field('avsight_certifications', $merged_product['certifications'] ?? '', $product_id);
        update_field('avsight_aircraft_model', $merged_product['aircraft_model'] ?? '', $product_id);
        update_field('avsight_engine_type', $merged_product['engine_type'] ?? '', $product_id);

        // ACF Fields - Inventory Details Tab
        update_field('avsight_total_quantity', $merged_product['total_quantity'], $product_id);
        update_field('avsight_average_cost', $merged_product['average_cost'], $product_id);

        // Get additional data from merged product
        $sample_data = self::extractAdditionalFieldsFromItem($merged_product);

        update_field('avsight_location', $sample_data['location'] ?? '', $product_id);
        update_field('avsight_uom', $sample_data['uom'] ?? '', $product_id);
        update_field('avsight_batch_lot', $sample_data['batch_lot'] ?? '', $product_id);
        update_field('avsight_protected', $sample_data['protected'] ?? false, $product_id);
        update_field('avsight_protected_reason', $sample_data['protected_reason'] ?? '', $product_id);
        update_field('avsight_serialized', $sample_data['serialized'] ?? false, $product_id);

        // Serial numbers (comma separated)
        $serial_numbers_text = !empty($merged_product['serial_numbers']) ? implode(', ', $merged_product['serial_numbers']) : '';
        update_field('avsight_serial_numbers', $serial_numbers_text, $product_id);

        // ACF Fields - Merging Information Tab
        update_field('avsight_item_count', $merged_product['item_count'], $product_id);
        update_field('avsight_product_sku', $product_sku, $product_id);
        update_field('avsight_last_sync', date('Y-m-d H:i:s'), $product_id);

        // Generate merge details for admin
        $merge_details = self::generateMergeDetails($merged_product);
        update_field('avsight_merge_details', $merge_details, $product_id);

        // ACF Fields - System Information Tab
        update_field('avsight_sync_source', 'Avsight API', $product_id);
        update_field('avsight_raw_data', json_encode($merged_product['inventory_items'], JSON_PRETTY_PRINT), $product_id);
    }

    /**
     * Extract additional fields from the merged product data
     *
     * @param array $merged_product Merged product group
     * @return array Additional field data
     */
    private static function extractAdditionalFieldsFromItem($merged_product)
    {
        // The merged product already contains these fields from the merger
        return [
            'location' => $merged_product['location'] ?? '',
            'uom' => $merged_product['uom'] ?? '',
            'batch_lot' => $merged_product['batch_lot'] ?? '',
            'protected' => $merged_product['protected'] ?? false,
            'protected_reason' => $merged_product['protected_reason'] ?? '',
            'serialized' => $merged_product['serialized'] ?? false
        ];
    }

    /**
     * Generate merge details text for admin display
     *
     * @param array $merged_product Merged product group
     * @return string Formatted merge details
     */
    private static function generateMergeDetails($merged_product)
    {
        $details = [];

        $details[] = "Merged Product Summary:";
        $details[] = "- Part Number: " . $merged_product['part_number'];
        $details[] = "- Warehouse: " . $merged_product['warehouse'];
        $details[] = "- Condition: " . $merged_product['condition'];
        $details[] = "- Certifications: " . ($merged_product['certifications'] ?? 'None');
        $details[] = "";
        $details[] = "Merging Results:";
        $details[] = "- Individual Items: " . $merged_product['item_count'];
        $details[] = "- Total Quantity: " . $merged_product['total_quantity'];
        $details[] = "- Average Cost: $" . number_format($merged_product['average_cost'], 2);
        $details[] = "";

        if (!empty($merged_product['serial_numbers'])) {
            $details[] = "Serial Numbers:";
            foreach ($merged_product['serial_numbers'] as $serial) {
                $details[] = "- " . $serial;
            }
            $details[] = "";
        }

        $details[] = "Individual Items:";
        foreach ($merged_product['inventory_items'] as $index => $item) {
            $details[] = ($index + 1) . ". " . ($item['name'] ?? $item['id']);
            $details[] = "   Quantity: " . $item['quantity'];
            $details[] = "   Cost: $" . number_format($item['cost'], 2);
            if (!empty($item['serial_number'])) {
                $details[] = "   Serial: " . $item['serial_number'];
            }
            $details[] = "";
        }

        return implode("\n", $details);
    }

    /**
     * Assign WooCommerce product attributes for filtering
     *
     * @param int $product_id WooCommerce product ID
     * @param array $merged_product Merged product group
     */
    private static function assignProductAttributes($product_id, $merged_product)
    {
        $product = wc_get_product($product_id);
        if (!$product) {
            return;
        }

        $attributes = [];

        // Application Code Aircraft attribute (from Applicability field)
        if (!empty($merged_product['aircraft_model'])) {
            $aircraft_models = self::parseAircraftModels($merged_product['aircraft_model']);
            if (!empty($aircraft_models)) {
                $attributes['pa_application-code-aircraft'] = [
                    'name' => 'pa_application-code-aircraft',
                    'value' => $aircraft_models,
                    'position' => 0,
                    'is_visible' => 1,
                    'is_variation' => 0,
                    'is_taxonomy' => 1
                ];
            }
        }

        // Engine Type attribute (if available)
        if (!empty($merged_product['engine_type'])) {
            $engine_types = self::parseEngineTypes($merged_product['engine_type']);
            if (!empty($engine_types)) {
                $attributes['pa_engine-type'] = [
                    'name' => 'pa_engine-type',
                    'value' => $engine_types,
                    'position' => 1,
                    'is_visible' => 1,
                    'is_variation' => 0,
                    'is_taxonomy' => 1
                ];
            }
        }

        // Condition attribute (always available) - use display condition name
        if (!empty($merged_product['condition'])) {
            $condition_mapping = AvsightApi::getConditionDisplayMapping();
            $raw_condition = $merged_product['condition'];
            $display_condition = $condition_mapping[$raw_condition] ?? $raw_condition;

            $attributes['pa_cond'] = [
                'name' => 'pa_cond',
                'value' => [$display_condition],
                'position' => 2,
                'is_visible' => 1,
                'is_variation' => 0,
                'is_taxonomy' => 1
            ];
        }

        // Assign attributes to product
        if (!empty($attributes)) {
            self::setProductAttributes($product, $attributes);
            $product->save();

            error_log('Avsight Sync: Assigned attributes to product ' . $product_id . ': ' . json_encode(array_keys($attributes)));
        }
    }

    /**
     * Parse aircraft models from Salesforce data
     *
     * @param string $aircraft_data Raw aircraft data from Salesforce
     * @return array Array of aircraft model terms
     */
    private static function parseAircraftModels($aircraft_data)
    {
        if (empty($aircraft_data)) {
            return [];
        }

        // Split by common delimiters and clean up
        $models = preg_split('/[,;|\/\s]+/', $aircraft_data);
        $cleaned_models = [];

        foreach ($models as $model) {
            $model = trim($model);
            if (!empty($model) && strlen($model) > 1) {
                $cleaned_models[] = $model;
            }
        }

        return array_unique($cleaned_models);
    }

    /**
     * Parse engine types from Salesforce data
     *
     * @param string $engine_data Raw engine data from Salesforce
     * @return array Array of engine type terms
     */
    private static function parseEngineTypes($engine_data)
    {
        if (empty($engine_data)) {
            return [];
        }

        // Split by common delimiters and clean up
        $types = preg_split('/[,;|\/\s]+/', $engine_data);
        $cleaned_types = [];

        foreach ($types as $type) {
            $type = trim($type);
            if (!empty($type) && strlen($type) > 1) {
                $cleaned_types[] = $type;
            }
        }

        return array_unique($cleaned_types);
    }

    /**
     * Set product attributes and create taxonomy terms
     *
     * @param \WC_Product $product WooCommerce product
     * @param array $attributes Attributes to assign
     */
    private static function setProductAttributes($product, $attributes)
    {
        foreach ($attributes as $attribute_name => $attribute_data) {
            // Ensure the taxonomy exists
            if (!taxonomy_exists($attribute_name)) {
                // Register the taxonomy if it doesn't exist
                self::registerProductAttributeTaxonomy($attribute_name);
            }

            // Create/get terms for this attribute
            $term_ids = [];
            foreach ($attribute_data['value'] as $term_name) {
                $term = get_term_by('name', $term_name, $attribute_name);
                if (!$term) {
                    // Create the term if it doesn't exist
                    $term_result = wp_insert_term($term_name, $attribute_name);
                    if (!is_wp_error($term_result)) {
                        $term_ids[] = $term_result['term_id'];
                    }
                } else {
                    $term_ids[] = $term->term_id;
                }
            }

            // Assign terms to product
            if (!empty($term_ids)) {
                wp_set_object_terms($product->get_id(), $term_ids, $attribute_name);
            }
        }

        // Update product attributes
        $product->set_attributes($attributes);
    }

    /**
     * Register a product attribute taxonomy if it doesn't exist
     *
     * @param string $attribute_name Attribute taxonomy name
     */
    private static function registerProductAttributeTaxonomy($attribute_name)
    {
        // Extract the attribute slug from the taxonomy name
        $attribute_slug = str_replace('pa_', '', $attribute_name);

        // Check if attribute exists in WooCommerce
        $attribute_id = wc_attribute_taxonomy_id_by_name($attribute_slug);

        if (!$attribute_id) {
            // Create the attribute in WooCommerce
            $attribute_data = [
                'name' => ucwords(str_replace('-', ' ', $attribute_slug)),
                'slug' => $attribute_slug,
                'type' => 'select',
                'order_by' => 'menu_order',
                'has_archives' => false,
            ];

            $attribute_id = wc_create_attribute($attribute_data);

            if (!is_wp_error($attribute_id)) {
                error_log('Avsight Sync: Created new product attribute: ' . $attribute_slug);
            }
        }
    }

    /**
     * Completes the sync process and cleans up
     */
    private static function completeSyncProcess()
    {
        error_log('Avsight Sync: Inventory synchronization completed successfully.');
        self::addSyncLogMessage('🎉 Inventory synchronization completed successfully!');

        // Debug: Count actual WooCommerce products with Avsight metadata
        $avsight_products = get_posts(array(
            'post_type' => 'product',
            'meta_key' => '_avsight_synced',
            'meta_value' => 'yes',
            'posts_per_page' => -1,
            'fields' => 'ids'
        ));
        $avsight_product_count = count($avsight_products);
        error_log('Avsight Sync Debug: Found ' . $avsight_product_count . ' products with Avsight metadata in WooCommerce');
        self::addSyncLogMessage('📊 Total Avsight products in WooCommerce: ' . $avsight_product_count);

        // Handle products that are no longer in Avsight
        self::handleRemovedProducts();

        // Update last successful sync timestamp for incremental sync
        update_option('avsight_last_successful_sync', gmdate('Y-m-d\TH:i:s\Z'));

        // Set end time
        set_transient(AVSIGHT_SYNC_LAST_RUN_END_KEY, time());

        // Clear running status
        delete_transient(AVSIGHT_SYNC_TRANSIENT_KEY);

        // Clean up temporary files
        self::deleteTempFileAndTransient(AVSIGHT_SYNC_DATA_FILE_PATH_KEY, AVSIGHT_SYNC_DATA_FILE_OFFSET_KEY);

        // Clear any sync errors
        delete_transient('avsight_sync_error');

        // Clean up removal tracking data
        delete_transient('avsight_current_sync_products');
    }

    /**
     * Writes data to a temporary file
     *
     * @param string $file_path_key Transient key for file path
     * @param array $data Data to write
     * @param bool $json_lines Whether to write as JSON lines format
     * @return string|false File path on success, false on failure
     */
    private static function writeDataToFile($file_path_key, $data, $json_lines = false)
    {
        $upload_dir = wp_upload_dir();
        $temp_dir = $upload_dir['basedir'] . '/avsight-sync-temp/';

        if (!file_exists($temp_dir)) {
            wp_mkdir_p($temp_dir);
        }

        $file_name = str_replace('avsight_', '', $file_path_key) . '.jsonl';
        $file_path = $temp_dir . $file_name;

        $file_handle = fopen($file_path, 'w');
        if (!$file_handle) {
            error_log('Avsight Sync Error: Could not open file for writing: ' . $file_path);
            return false;
        }

        if ($json_lines) {
            foreach ($data as $item) {
                fwrite($file_handle, json_encode($item) . "\n");
            }
        } else {
            fwrite($file_handle, json_encode($data));
        }

        fclose($file_handle);

        set_transient($file_path_key, $file_path, DAY_IN_SECONDS);
        error_log('Avsight Sync: Successfully wrote ' . count($data) . ' items to temporary file: ' . $file_path . ' for key: ' . $file_path_key);

        return $file_path;
    }

    /**
     * Reads lines from a file
     *
     * @param string $file_path Path to file
     * @param int $start_line Starting line number (0-based)
     * @param int $max_lines Maximum lines to read
     * @return array Array with 'items' and 'next_line_to_process'
     */
    private static function readLinesFromFile($file_path, $start_line, $max_lines)
    {
        $items = [];
        $current_line = 0;
        $lines_read = 0;

        $file_handle = fopen($file_path, 'r');
        if (!$file_handle) {
            error_log('Avsight Sync Error: Could not open file for reading: ' . $file_path);
            return ['items' => [], 'next_line_to_process' => $start_line];
        }

        while (($line = fgets($file_handle)) !== false && $lines_read < $max_lines) {
            if ($current_line >= $start_line) {
                $item = json_decode(trim($line), true);
                if ($item !== null) {
                    $items[] = $item;
                    $lines_read++;
                }
            }
            $current_line++;
        }

        fclose($file_handle);

        return [
            'items' => $items,
            'next_line_to_process' => $start_line + $lines_read
        ];
    }

    /**
     * Deletes temporary file and associated transient
     *
     * @param string $file_path_key Transient key for file path
     * @param string $offset_key Optional offset transient key to delete
     */
    private static function deleteTempFileAndTransient($file_path_key, $offset_key = null)
    {
        $file_path = get_transient($file_path_key);
        if ($file_path && file_exists($file_path)) {
            unlink($file_path);
            error_log('Avsight Sync: Deleted temporary file: ' . $file_path);
        }

        delete_transient($file_path_key);

        if ($offset_key) {
            delete_transient($offset_key);
        }
    }

    /**
     * Adds a message to the sync log
     *
     * @param string $message Log message
     */
    private static function addSyncLogMessage($message)
    {
        $log_messages = get_transient('avsight_sync_log_messages');
        if (!$log_messages) {
            $log_messages = [];
        }

        $log_messages[] = [
            'timestamp' => current_time('mysql'),
            'message' => $message
        ];

        // Keep only last 50 messages
        if (count($log_messages) > 50) {
            $log_messages = array_slice($log_messages, -50);
        }

        set_transient('avsight_sync_log_messages', $log_messages, HOUR_IN_SECONDS);
    }

    /**
     * Clears sync log messages
     */
    private static function clearSyncLog()
    {
        delete_transient('avsight_sync_log_messages');
    }

    /**
     * Emergency recovery: Restore products that were incorrectly set to out of stock
     * This should only be used after the incremental sync bug that set all products to out of stock
     */
    public static function emergencyRestoreProducts()
    {
        error_log('Avsight Sync: Starting emergency product restoration...');

        // Get all Avsight products that were set to out of stock in the last hour
        $avsight_products = get_posts([
            'post_type' => 'product',
            'posts_per_page' => -1,
            'meta_query' => [
                [
                    'key' => '_avsight_synced',
                    'value' => 'yes',
                    'compare' => '='
                ]
            ],
            'fields' => 'ids'
        ]);

        $restored_count = 0;

        foreach ($avsight_products as $product_id) {
            $product = wc_get_product($product_id);
            if ($product && $product->get_stock_status() === 'outofstock') {
                // Check if this product had stock before (look for previous stock quantity)
                $stock_quantity = $product->get_stock_quantity();

                // If stock quantity is null or 0, set it to 1 as a safe default
                if ($stock_quantity === null || $stock_quantity <= 0) {
                    $product->set_stock_quantity(1);
                }

                // Set back to in stock
                $product->set_stock_status('instock');
                $product->save();

                $restored_count++;
                error_log('Avsight Sync: Restored product ID ' . $product_id . ' to in stock');
            }
        }

        error_log('Avsight Sync: Emergency restoration completed. Restored ' . $restored_count . ' products');
        return $restored_count;
    }

    /**
     * Process a single sync batch (for background AJAX processing)
     *
     * @param int|null $offset Current offset in the data file
     * @return array Result with success status and data
     */
    public static function processSyncBatch($offset = null)
    {
        try {
            // Check if sync is running
            if (!get_transient(AVSIGHT_SYNC_TRANSIENT_KEY)) {
                return [
                    'success' => false,
                    'message' => 'No sync process running',
                    'completed' => true
                ];
            }

            // Get data file path
            $data_file_path = get_transient(AVSIGHT_SYNC_DATA_FILE_PATH_KEY);
            if (!$data_file_path || !file_exists($data_file_path)) {
                return [
                    'success' => false,
                    'message' => 'Sync data file not found',
                    'completed' => true
                ];
            }

            // Use provided offset or get from transient
            if ($offset === null) {
                $offset = (int) get_transient(AVSIGHT_SYNC_DATA_FILE_OFFSET_KEY);
            }

            // Process the batch
            $result = self::processSingleBatch($data_file_path, $offset);

            if ($result['completed']) {
                // Sync completed, handle final steps
                self::handleRemovedProducts();
                self::completeSyncProcess();

                return [
                    'success' => true,
                    'message' => 'Sync completed successfully',
                    'completed' => true,
                    'total' => $result['total'],
                    'processed' => $result['processed']
                ];
            } else {
                // More batches to process
                return [
                    'success' => true,
                    'message' => 'Batch processed successfully',
                    'completed' => false,
                    'total' => $result['total'],
                    'processed' => $result['processed'],
                    'next_offset' => $result['next_offset']
                ];
            }
        } catch (\Exception $e) {
            error_log('Avsight Sync Error: Exception in processSyncBatch: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error processing sync batch: ' . $e->getMessage(),
                'completed' => true
            ];
        }
    }

    /**
     * Process a single batch of sync data
     *
     * @param string $data_file_path Path to the data file
     * @param int $offset Current offset in the file
     * @return array Result with completion status and progress
     */
    private static function processSingleBatch($data_file_path, $offset)
    {
        // Read batch from file using configurable batch size
        $sync_batch_size = get_option('avsight_sync_batch_size', AVSIGHT_SYNC_BATCH_SIZE);
        $file_read_result = self::readLinesFromFile($data_file_path, $offset, $sync_batch_size);

        $batch_items = $file_read_result['items'];
        $new_offset = $file_read_result['next_line_to_process'];
        $total_items = (int) get_transient(AVSIGHT_SYNC_TOTAL_ITEMS_KEY);

        // Process each item in the batch
        $batch_processed = 0;
        $batch_failed = 0;
        $batch_created = 0;
        $batch_updated = 0;

        foreach ($batch_items as $item) {
            $result = self::updateWooCommerceProduct($item);
            if ($result === 'created') {
                $batch_processed++;
                $batch_created++;
            } elseif ($result === 'updated') {
                $batch_processed++;
                $batch_updated++;
            } else {
                $batch_failed++;
            }
        }

        // Update progress
        $current_processed = (int) get_transient(AVSIGHT_SYNC_PROCESSED_ITEMS_KEY);
        $new_processed_count = $current_processed + $batch_processed;
        set_transient(AVSIGHT_SYNC_PROCESSED_ITEMS_KEY, $new_processed_count, HOUR_IN_SECONDS);
        set_transient(AVSIGHT_SYNC_DATA_FILE_OFFSET_KEY, $new_offset, HOUR_IN_SECONDS);

        // Log progress
        error_log('Avsight Sync: Batch processed. Success: ' . $batch_processed . ', Failed: ' . $batch_failed . ', Created: ' . $batch_created . ', Updated: ' . $batch_updated . '. Total progress: ' . $new_processed_count . '/' . $total_items);

        $log_message = '📦 Processed batch: ' . $batch_processed . '/' . count($batch_items) . ' items';
        if ($batch_failed > 0) {
            $log_message .= ' (⚠️ ' . $batch_failed . ' failed)';
        }
        if ($batch_created > 0) {
            $log_message .= ' (🆕 ' . $batch_created . ' created)';
        }
        if ($batch_updated > 0) {
            $log_message .= ' (🔄 ' . $batch_updated . ' updated)';
        }
        $log_message .= '. Total: ' . $new_processed_count . '/' . $total_items . ' (' . round(($new_processed_count / $total_items) * 100) . '%)';

        self::addSyncLogMessage($log_message);

        // Check if completed (no more items to read or reached total)
        $completed = ($new_processed_count >= $total_items) || empty($batch_items);

        return [
            'success' => true,
            'completed' => $completed,
            'total' => $total_items,
            'processed' => $new_processed_count,
            'next_offset' => $new_offset
        ];
    }

    /**
     * Track products in current sync for removal handling
     *
     * @param array $results Current sync results (raw or merged)
     * @param bool $is_merged Whether the results are merged products
     */
    private static function trackCurrentSyncProducts($results, $is_merged = false)
    {
        $current_sync_products = [];

        foreach ($results as $item) {
            if ($is_merged) {
                // Handle merged product format - use the generated SKU for tracking
                $sku = AvsightProductMerger::generateSku($item);
                $current_sync_products[] = $sku;
            } else {
                // Handle raw Salesforce data format
                if (isset($item['inscor__Product__r.Name'])) {
                    $current_sync_products[] = $item['inscor__Product__r.Name'];
                } elseif (isset($item['Name'])) {
                    $current_sync_products[] = $item['Name'];
                }
            }
        }

        // Store current sync products list
        set_transient('avsight_current_sync_products', $current_sync_products, HOUR_IN_SECONDS);

        error_log('Avsight Sync Debug: Tracked ' . count($current_sync_products) . ' products in current sync (' . ($is_merged ? 'merged SKUs' : 'raw names') . ')');
    }

    /**
     * Handle products that are no longer in Avsight inventory
     */
    public static function handleRemovedProducts()
    {
        error_log('Avsight Sync: Starting removed products handling...');
        self::addSyncLogMessage('🗑️ Checking for products to remove...');

        // Get current sync products
        $current_sync_products = get_transient('avsight_current_sync_products');
        if (!$current_sync_products) {
            $current_sync_products = []; // Empty array for incremental syncs with no changes
        }

        // Check if this is a limited sync (debug mode with sync limit)
        $debug_mode = get_option('avsight_debug_mode', false);
        $sync_limit = get_option('avsight_sync_limit', 0);
        $is_limited_sync = $debug_mode && $sync_limit > 0;
        $safe_mode = get_option('avsight_safe_mode_limited_sync', true);

        if ($is_limited_sync) {
            if ($safe_mode) {
                self::addSyncLogMessage('⚠️ Debug mode: Limited sync (' . $sync_limit . ' products) - Safe mode enabled, will skip product removal');
            } else {
                self::addSyncLogMessage('⚠️ Debug mode: Limited sync (' . $sync_limit . ' products) - Safe mode disabled, will remove products not in sync');
            }
        }

        self::addSyncLogMessage('🔍 Found ' . count($current_sync_products) . ' products in current sync to compare against existing products');

        // Get all WooCommerce products that were synced from Avsight
        $avsight_products = get_posts([
            'post_type' => 'product',
            'posts_per_page' => -1,
            'meta_query' => [
                [
                    'key' => '_avsight_synced',
                    'value' => 'yes',
                    'compare' => '='
                ]
            ],
            'fields' => 'ids'
        ]);

        $removed_count = 0;
        $out_of_stock_count = 0;
        $skipped_count = 0;

        foreach ($avsight_products as $product_id) {
            // Get the product to check its SKU (for merged products) or name (for legacy products)
            $product = wc_get_product($product_id);
            if (!$product) {
                continue;
            }

            $product_sku = $product->get_sku();
            $avsight_product_name = get_post_meta($product_id, '_avsight_product_name', true);

            // For merged products, compare by SKU; for legacy products, compare by name
            $product_identifier = !empty($product_sku) ? $product_sku : $avsight_product_name;

            // Check if this product is still in current sync
            if (!in_array($product_identifier, $current_sync_products)) {
                // For limited syncs, be more cautious about removal
                $safe_mode = get_option('avsight_safe_mode_limited_sync', true);
                if ($is_limited_sync && $safe_mode) {
                    // Skip removal for limited syncs to avoid accidentally removing products
                    // that just weren't included in the limited batch
                    $skipped_count++;
                    continue;
                }

                // Product is no longer in Avsight, handle removal
                $handled = self::handleRemovedProduct($product_id, $avsight_product_name);

                if ($handled === 'removed') {
                    $removed_count++;
                } elseif ($handled === 'out_of_stock') {
                    $out_of_stock_count++;
                }
            }
        }

        $message = "Removed products handling complete. Removed: {$removed_count}, Set out of stock: {$out_of_stock_count}";
        if ($skipped_count > 0) {
            $message .= ", Skipped: {$skipped_count} (limited sync protection)";
        }
        error_log('Avsight Sync: ' . $message);
        self::addSyncLogMessage('✅ ' . $message);
    }

    /**
     * Handle a single removed product
     *
     * @param int $product_id WooCommerce product ID
     * @param string $product_name Avsight product name
     * @return string 'removed', 'out_of_stock', or 'error'
     */
    private static function handleRemovedProduct($product_id, $product_name)
    {
        $removal_action = get_option('avsight_removal_action', 'out_of_stock');

        $product = wc_get_product($product_id);
        if (!$product) {
            error_log('Avsight Sync Error: Could not load product ID ' . $product_id . ' for removal');
            return 'error';
        }

        try {
            switch ($removal_action) {
                case 'delete':
                    // Permanently delete the product
                    $result = wp_delete_post($product_id, true);
                    if ($result) {
                        error_log('Avsight Sync: Deleted product "' . $product_name . '" (ID: ' . $product_id . ') - no longer in Avsight');
                        return 'removed';
                    } else {
                        error_log('Avsight Sync Error: Failed to delete product ID ' . $product_id);
                        return 'error';
                    }

                case 'disable':
                    // Set product to draft status (hidden from customers)
                    wp_update_post([
                        'ID' => $product_id,
                        'post_status' => 'draft'
                    ]);

                    // Add note to product description
                    $current_description = $product->get_description();
                    if (strpos($current_description, 'No longer available in Avsight') === false) {
                        $updated_description = $current_description . "\n\n[AVSIGHT SYNC] No longer available in Avsight inventory as of " . date('Y-m-d H:i:s');
                        $product->set_description($updated_description);
                        $product->save();
                    }

                    // Update metadata
                    update_post_meta($product_id, '_avsight_removed_date', time());
                    update_post_meta($product_id, '_avsight_removal_reason', 'not_in_sync');

                    error_log('Avsight Sync: Disabled product "' . $product_name . '" (ID: ' . $product_id . ') - no longer in Avsight');
                    return 'disabled';

                case 'out_of_stock':
                default:
                    // Set product out of stock (default and safest option)
                    $product->set_stock_quantity(0);
                    $product->set_stock_status('outofstock');
                    $product->set_manage_stock(true);

                    // Add note to product description
                    $current_description = $product->get_description();
                    if (strpos($current_description, 'No longer available in Avsight') === false) {
                        $updated_description = $current_description . "\n\n[AVSIGHT SYNC] No longer available in Avsight inventory as of " . date('Y-m-d H:i:s');
                        $product->set_description($updated_description);
                    }

                    // Update metadata
                    update_post_meta($product_id, '_avsight_removed_date', time());
                    update_post_meta($product_id, '_avsight_removal_reason', 'not_in_sync');

                    $result = $product->save();

                    if (!is_wp_error($result)) {
                        error_log('Avsight Sync: Set product "' . $product_name . '" (ID: ' . $product_id . ') out of stock - no longer in Avsight');
                        return 'out_of_stock';
                    } else {
                        error_log('Avsight Sync Error: Failed to update removed product ID ' . $product_id);
                        return 'error';
                    }
            }
        } catch (\Exception $e) {
            error_log('Avsight Sync Error: Exception handling removed product "' . $product_name . '": ' . $e->getMessage());
            return 'error';
        }
    }
}
