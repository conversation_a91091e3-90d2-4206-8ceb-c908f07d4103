<?php

namespace App\AvsightSync;

/**
 * <PERSON>les AJAX requests for Avsight sync operations
 */
class AvsightAjax
{
    /**
     * Initialize AJAX handlers
     */
    public static function init()
    {
        // Sync status AJAX handler
        add_action('wp_ajax_avsight_get_sync_status', [self::class, 'getSyncStatusHandler']);

        // Stop sync AJAX handler
        add_action('wp_ajax_avsight_stop_sync', [self::class, 'stopSyncHandler']);

        // Sync batch processing AJAX handler
        add_action('wp_ajax_avsight_ajax_process_sync_batch', [self::class, 'processSyncBatchHandler']);
        add_action('wp_ajax_nopriv_avsight_ajax_process_sync_batch', [self::class, 'processSyncBatchHandler']);

        // Delete batch processing AJAX handler
        add_action('wp_ajax_avsight_ajax_process_delete_batch', [self::class, 'processDeleteBatchHandler']);
        add_action('wp_ajax_nopriv_avsight_ajax_process_delete_batch', [self::class, 'processDeleteBatchHandler']);

        // Stop delete AJAX handler
        add_action('wp_ajax_avsight_stop_bulk_delete', [self::class, 'stopBulkDeleteHandler']);
    }

    /**
     * AJAX handler for getting sync status
     */
    public static function getSyncStatusHandler()
    {
        // Clean any previous output that might interfere with JSON response
        if (ob_get_length()) {
            ob_clean();
        }

        // Suppress any PHP warnings/notices that might interfere with JSON
        error_reporting(E_ERROR | E_PARSE);

        check_ajax_referer('avsight_sync_status_nonce', 'nonce');

        // Get sync status
        $sync_status = self::getSyncStatus();

        // Get delete status
        $delete_status = AvsightDeleteOperations::getDeleteStatus();

        $response_data = [
            'sync_status' => $sync_status,
            'delete_status' => $delete_status,
            'error' => get_transient('avsight_sync_error'),
            'delete_error' => get_transient('avsight_delete_error'),
        ];

        wp_send_json($response_data);
    }

    /**
     * AJAX handler for stopping sync process
     */
    public static function stopSyncHandler()
    {
        check_ajax_referer('avsight_sync_status_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Insufficient permissions.']);
            return;
        }

        // Clear ALL scheduled events related to sync
        wp_clear_scheduled_hook('avsight_process_inventory_batch_event');
        wp_clear_scheduled_hook('avsight_monitor_bulk_query_event');

        // Clear the main sync running transient FIRST
        delete_transient(AVSIGHT_SYNC_TRANSIENT_KEY);

        // Clear bulk query transients
        delete_transient('avsight_bulk_query_job_id');
        delete_transient('avsight_bulk_query_access_token');

        // Set end time
        set_transient(AVSIGHT_SYNC_LAST_RUN_END_KEY, time());

        // Clean up temporary files
        self::deleteTempFileAndTransient(AVSIGHT_SYNC_DATA_FILE_PATH_KEY, AVSIGHT_SYNC_DATA_FILE_OFFSET_KEY);
        self::deleteTempFileAndTransient(AVSIGHT_ALL_PRODUCT_IDS_FILE_PATH_KEY);
        self::deleteTempFileAndTransient(AVSIGHT_SYNCED_PRODUCT_IDS_FILE_PATH_KEY);
        self::deleteTempFileAndTransient(AVSIGHT_UNSYNCED_IDS_FILE_PATH_KEY, AVSIGHT_UNSYNCED_IDS_FILE_OFFSET_KEY);

        // Clear sync progress transients to reset the display
        delete_transient(AVSIGHT_SYNC_TOTAL_ITEMS_KEY);
        delete_transient(AVSIGHT_SYNC_PROCESSED_ITEMS_KEY);

        // Clear any sync errors
        delete_transient('avsight_sync_error');

        error_log('Avsight Sync: Manual sync stop initiated. All sync transients, scheduled batches, and progress data cleared.');

        wp_send_json_success(['message' => 'Inventory sync process stopped.']);
    }

    /**
     * AJAX handler for processing sync batches
     */
    public static function processSyncBatchHandler()
    {
        // Clean any previous output that might interfere with JSON response
        if (ob_get_length()) {
            ob_clean();
        }

        check_ajax_referer('avsight_sync_status_nonce', 'nonce');

        set_time_limit(300); // Allow up to 5 minutes for this batch

        $offset = isset($_POST['offset']) ? (int) $_POST['offset'] : null;

        $result = AvsightSyncOperations::processSyncBatch($offset);

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result);
        }
    }

    /**
     * AJAX handler for processing delete batches
     */
    public static function processDeleteBatchHandler()
    {
        // Clean any previous output that might interfere with JSON response
        if (ob_get_length()) {
            ob_clean();
        }

        check_ajax_referer('avsight_sync_status_nonce', 'nonce');

        set_time_limit(300); // Allow up to 5 minutes for this batch

        $offset = isset($_POST['offset']) ? (int) $_POST['offset'] : null;

        $result = AvsightDeleteOperations::processDeleteBatch($offset);

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result);
        }
    }

    /**
     * AJAX handler for stopping bulk delete process
     */
    public static function stopBulkDeleteHandler()
    {
        check_ajax_referer('avsight_sync_status_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Insufficient permissions.']);
            return;
        }

        $result = AvsightDeleteOperations::stopBulkDelete();

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result);
        }
    }

    /**
     * Gets current sync status
     *
     * @return array Sync status information
     */
    private static function getSyncStatus()
    {
        $total_items = (int) get_transient(AVSIGHT_SYNC_TOTAL_ITEMS_KEY);
        $processed_items = (int) get_transient(AVSIGHT_SYNC_PROCESSED_ITEMS_KEY);
        $last_run_start = get_transient(AVSIGHT_SYNC_LAST_RUN_START_KEY);
        $last_run_end = get_transient(AVSIGHT_SYNC_LAST_RUN_END_KEY);

        // Determine if sync is currently running
        // Check if sync transient is set OR if we're monitoring a bulk query
        $bulk_query_job_id = get_transient('avsight_bulk_query_job_id');
        $is_running = get_transient(AVSIGHT_SYNC_TRANSIENT_KEY) || $bulk_query_job_id;

        // Determine if we're truly in waiting phase vs processing phase
        // We're only "waiting" if we have a bulk query job AND no items have been processed yet
        $is_waiting_phase = $bulk_query_job_id && $total_items == 0 && $processed_items == 0;

        $status = [
            'running' => $is_running,
            'total' => $total_items,
            'processed' => $processed_items,
            'percentage' => $total_items > 0 ? round(($processed_items / $total_items) * 100, 2) : 0,
            'time_elapsed' => 0,
            'time_remaining' => '00:00:00',
            'next_batch_in' => 'N/A',
            'log' => self::getSyncLog(), // Add live log data
            'bulk_query_monitoring' => $is_waiting_phase, // Only true if actually waiting
        ];

        // Calculate time elapsed and remaining if sync is running or recently completed
        if ($last_run_start) {
            $current_time = $last_run_end && !$is_running ? $last_run_end : time();
            $elapsed_seconds = $current_time - $last_run_start;
            $status['time_elapsed'] = self::formatTime($elapsed_seconds);

            // Calculate estimated time remaining if sync is running and we have progress
            if ($is_running && $processed_items > 0 && $total_items > 0) {
                $items_per_second = $processed_items / $elapsed_seconds;
                $remaining_items = $total_items - $processed_items;
                $estimated_seconds = $items_per_second > 0 ? $remaining_items / $items_per_second : 0;
                $status['time_remaining'] = self::formatTime($estimated_seconds);
            } elseif (!$is_running) {
                $status['time_remaining'] = '00:00:00';
            }

            // Calculate next batch timing
            if ($is_running) {
                $next_batch_timestamp = wp_next_scheduled('avsight_process_inventory_batch_event');
                if ($next_batch_timestamp) {
                    $status['next_batch_in'] = max(0, $next_batch_timestamp - time());
                } else {
                    $status['next_batch_in'] = 0;
                }
            }
        }

        return $status;
    }

    /**
     * Formats time in seconds to HH:MM:SS format
     *
     * @param int $seconds Time in seconds
     * @return string Formatted time string
     */
    private static function formatTime($seconds)
    {
        // Ensure input is numeric and convert to float first
        $seconds = (float) $seconds;

        if ($seconds <= 0) {
            return '00:00:00';
        }

        $hours = (int) floor($seconds / 3600);
        $minutes = (int) floor(($seconds % 3600) / 60);
        $seconds = (int) floor($seconds % 60);

        return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    }

    /**
     * Deletes temporary file and associated transient
     *
     * @param string $file_path_key Transient key for file path
     * @param string $offset_key Optional offset transient key to delete
     */
    private static function deleteTempFileAndTransient($file_path_key, $offset_key = null)
    {
        $file_path = get_transient($file_path_key);
        if ($file_path && file_exists($file_path)) {
            unlink($file_path);
            error_log('Avsight Sync: Deleted temporary file: ' . $file_path);
        }

        delete_transient($file_path_key);

        if ($offset_key) {
            delete_transient($offset_key);
        }
    }

    /**
     * Gets sync log messages
     *
     * @return array Log messages
     */
    private static function getSyncLog()
    {
        $log_messages = get_transient('avsight_sync_log_messages');
        return $log_messages ? $log_messages : [];
    }
}
