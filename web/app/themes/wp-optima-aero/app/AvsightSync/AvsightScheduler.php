<?php

namespace App\AvsightSync;

/**
 * Handles scheduled sync operations
 */
class AvsightScheduler
{
    /**
     * Initialize scheduler
     */
    public static function init()
    {
        // Register the daily sync hook
        add_action('daily_avsight_inventory_sync', [AvsightSyncOperations::class, 'runInventorySync']);

        // Register background processing hooks
        add_action('avsight_process_inventory_batch_event', [AvsightSyncOperations::class, 'processInventoryBatch']);
        add_action('avsight_monitor_bulk_query_event', [AvsightSyncOperations::class, 'monitorBulkQuery']);

        // Schedule the daily sync if not already scheduled
        self::scheduleDailySync();

        // Add admin actions for manual schedule management
        add_action('admin_init', [self::class, 'handleScheduleActions']);
    }

    /**
     * Schedule the daily sync at 2 AM
     */
    public static function scheduleDailySync()
    {
        // Check if already scheduled
        if (wp_next_scheduled('daily_avsight_inventory_sync')) {
            return;
        }

        // Calculate next 2 AM
        $next_2am = self::getNext2AM();

        // Schedule the event
        $scheduled = wp_schedule_event($next_2am, 'daily', 'daily_avsight_inventory_sync');

        if ($scheduled !== false) {
            error_log('Avsight Sync: Daily sync scheduled for ' . date('Y-m-d H:i:s', $next_2am));
        } else {
            error_log('Avsight Sync: Failed to schedule daily sync');
        }
    }

    /**
     * Get the next 2 AM timestamp
     */
    private static function getNext2AM()
    {
        $timezone = wp_timezone();
        $now = new \DateTime('now', $timezone);

        // Set to 2 AM today
        $target = new \DateTime('today 02:00:00', $timezone);

        // If 2 AM today has already passed, schedule for tomorrow
        if ($target <= $now) {
            $target->modify('+1 day');
        }

        return $target->getTimestamp();
    }

    /**
     * Unschedule the daily sync
     */
    public static function unscheduleDailySync()
    {
        $timestamp = wp_next_scheduled('daily_avsight_inventory_sync');
        if ($timestamp) {
            wp_unschedule_event($timestamp, 'daily_avsight_inventory_sync');
            error_log('Avsight Sync: Daily sync unscheduled');
        }
    }

    /**
     * Reschedule the daily sync (useful for changing time)
     */
    public static function rescheduleDailySync()
    {
        self::unscheduleDailySync();
        self::scheduleDailySync();
    }

    /**
     * Get schedule status information
     */
    public static function getScheduleStatus()
    {
        $next_scheduled = wp_next_scheduled('daily_avsight_inventory_sync');
        $next_batch = wp_next_scheduled('avsight_process_inventory_batch_event');

        return [
            'daily_sync' => [
                'scheduled' => (bool) $next_scheduled,
                'next_run' => $next_scheduled,
                'next_run_formatted' => $next_scheduled ?
                    date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $next_scheduled) :
                    'Not scheduled',
                'time_until' => $next_scheduled ? self::formatTimeUntil($next_scheduled) : 'N/A'
            ],
            'batch_processing' => [
                'active' => (bool) $next_batch,
                'next_batch' => $next_batch,
                'next_batch_formatted' => $next_batch ?
                    date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $next_batch) :
                    'No active batch',
                'time_until' => $next_batch ? self::formatTimeUntil($next_batch) : 'N/A'
            ]
        ];
    }

    /**
     * Format time until next event
     */
    private static function formatTimeUntil($timestamp)
    {
        $diff = $timestamp - time();

        if ($diff <= 0) {
            return 'Overdue';
        }

        $hours = floor($diff / 3600);
        $minutes = floor(($diff % 3600) / 60);

        if ($hours > 24) {
            $days = floor($hours / 24);
            $hours = $hours % 24;
            return sprintf('%d day%s, %d hour%s', $days, $days !== 1 ? 's' : '', $hours, $hours !== 1 ? 's' : '');
        } elseif ($hours > 0) {
            return sprintf('%d hour%s, %d minute%s', $hours, $hours !== 1 ? 's' : '', $minutes, $minutes !== 1 ? 's' : '');
        } else {
            return sprintf('%d minute%s', $minutes, $minutes !== 1 ? 's' : '');
        }
    }

    /**
     * Handle admin actions for schedule management
     */
    public static function handleScheduleActions()
    {
        // Handle reschedule request
        if (isset($_POST['avsight_reschedule_nonce']) && wp_verify_nonce(sanitize_key(wp_unslash($_POST['avsight_reschedule_nonce'])), 'avsight_reschedule_sync')) {
            if (current_user_can('manage_options')) {
                self::rescheduleDailySync();
                add_action('admin_notices', function () {
                    echo '<div class="notice notice-success is-dismissible"><p><strong>Schedule Updated:</strong> Daily sync rescheduled for next 2 AM.</p></div>';
                });
            }
        }

        // Handle unschedule request
        if (isset($_POST['avsight_unschedule_nonce']) && wp_verify_nonce(sanitize_key(wp_unslash($_POST['avsight_unschedule_nonce'])), 'avsight_unschedule_sync')) {
            if (current_user_can('manage_options')) {
                self::unscheduleDailySync();
                add_action('admin_notices', function () {
                    echo '<div class="notice notice-warning is-dismissible"><p><strong>Schedule Removed:</strong> Daily sync has been unscheduled.</p></div>';
                });
            }
        }

        // Handle schedule request
        if (isset($_POST['avsight_schedule_nonce']) && wp_verify_nonce(sanitize_key(wp_unslash($_POST['avsight_schedule_nonce'])), 'avsight_schedule_sync')) {
            if (current_user_can('manage_options')) {
                self::scheduleDailySync();
                add_action('admin_notices', function () {
                    echo '<div class="notice notice-success is-dismissible"><p><strong>Schedule Created:</strong> Daily sync scheduled for next 2 AM.</p></div>';
                });
            }
        }
    }

    /**
     * Get cron schedule information for debugging
     */
    public static function getCronInfo()
    {
        $crons = _get_cron_array();
        $avsight_crons = [];

        foreach ($crons as $timestamp => $cron) {
            foreach ($cron as $hook => $events) {
                if (strpos($hook, 'avsight') !== false || strpos($hook, 'daily_avsight') !== false) {
                    $avsight_crons[] = [
                        'hook' => $hook,
                        'timestamp' => $timestamp,
                        'formatted' => date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $timestamp),
                        'events' => count($events)
                    ];
                }
            }
        }

        return $avsight_crons;
    }

    /**
     * Check if WordPress cron is working
     */
    public static function isCronWorking()
    {
        // Check if DISABLE_WP_CRON is set
        if (defined('DISABLE_WP_CRON') && DISABLE_WP_CRON) {
            return [
                'working' => false,
                'message' => 'WordPress cron is disabled (DISABLE_WP_CRON is true). You need to set up a system cron job.'
            ];
        }

        // Check if there are any scheduled events
        $crons = _get_cron_array();
        if (empty($crons)) {
            return [
                'working' => false,
                'message' => 'No cron events are scheduled. This might indicate a cron system issue.'
            ];
        }

        // Check if wp-cron.php is accessible
        $cron_url = site_url('wp-cron.php');
        $response = wp_remote_get($cron_url, [
            'timeout' => 10,
            'blocking' => true
        ]);

        if (is_wp_error($response)) {
            return [
                'working' => false,
                'message' => 'Cannot access wp-cron.php: ' . $response->get_error_message()
            ];
        }

        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            return [
                'working' => false,
                'message' => 'wp-cron.php returned HTTP ' . $response_code . ' instead of 200'
            ];
        }

        return [
            'working' => true,
            'message' => 'WordPress cron appears to be working correctly'
        ];
    }

    /**
     * Force run the daily sync (for testing)
     */
    public static function forceRunDailySync()
    {
        if (!current_user_can('manage_options')) {
            return false;
        }

        // Run the sync immediately
        AvsightSyncOperations::runInventorySync();

        error_log('Avsight Sync: Daily sync manually triggered');
        return true;
    }
}
