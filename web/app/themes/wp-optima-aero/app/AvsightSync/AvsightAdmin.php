<?php

namespace App\AvsightSync;

/**
 * <PERSON>les admin interface for Avsight sync
 */
class AvsightAdmin
{
    /**
     * Initialize admin interface
     */
    public static function init()
    {
        add_action('admin_menu', [self::class, 'addAdminMenu']);
        add_action('admin_enqueue_scripts', [self::class, 'enqueueScripts']);
    }

    /**
     * Add admin menu page
     */
    public static function addAdminMenu()
    {
        add_menu_page(
            'Avsight Sync',
            'Avsight Sync',
            'manage_options',
            'avsight-sync',
            [self::class, 'renderAdminPage'],
            'dashicons-update',
            30
        );
    }

    /**
     * Enqueue admin scripts and styles
     */
    public static function enqueueScripts($hook)
    {
        if ($hook !== 'toplevel_page_avsight-sync') {
            return;
        }

        wp_enqueue_script('jquery');

        // Add inline script for AJAX
        $nonce = wp_create_nonce('avsight_sync_status_nonce');
        $script = "
        var ajaxurl = '" . admin_url('admin-ajax.php') . "';
        var nonce = '" . $nonce . "';
        ";
        wp_add_inline_script('jquery', $script, 'before');
    }

    /**
     * Debug current sync fields - shows exactly what fields are returned by the current sync query
     */
    private static function debugCurrentSyncFields($access_token)
    {
        echo '<div class="notice notice-info is-dismissible">';
        echo '<h3>🔍 Current Sync Fields Debug</h3>';

        try {
            // Use the exact same query that the sync uses, but limit to 3 records
            $query = "SELECT Id, Name, inscor__Product__c, inscor__Product__r.Name, inscor__Location__c, inscor__Location__r.Name, inscor__Sub_Location__c, inscor__Warehouse__c, inscor__Warehouse__r.Name, inscor__Condition_Code__c, inscor__Condition_Code__r.Name, inscor__Owner_Code__c, inscor__Owner_Code__r.Name, inscor__Serial_Number__c, inscor__Keyword__c, inscor__Quantity_Available__c, inscor__Quantity__c, inscor__Acquisition_Cost__c, inscor__UOM__c, inscor__Batch_LOT__c, inscor__Protected__c, inscor__Protected_Reason__c, inscor__Serialized__c, inscor__Grouping_Enabled__c, inscor__Create_Date__c, inscor__Comments__c, LastModifiedDate, inscor__Repair_Type__c, inscor__Trace_Type__c, inscor__Public_Use_Aircraft__c FROM inscor__Inventory_Line__c WHERE inscor__Product__r.Name != null AND inscor__Quantity_Available__c > 0 AND inscor__Product__c != null LIMIT 3";

            $query_url = 'https://prod-avs-optimaaero--oasb.sandbox.my.salesforce.com/services/data/v55.0/query/?q=' . urlencode($query);

            $response = wp_remote_get($query_url, array(
                'timeout' => 30,
                'headers' => array(
                    'Authorization' => 'Bearer ' . $access_token,
                    'Accept' => 'application/json',
                ),
            ));

            if (is_wp_error($response)) {
                echo '<p><strong>❌ Error:</strong> ' . esc_html($response->get_error_message()) . '</p>';
                echo '</div>';
                return;
            }

            $response_code = wp_remote_retrieve_response_code($response);
            $response_body = wp_remote_retrieve_body($response);

            if ($response_code !== 200) {
                echo '<p><strong>❌ HTTP Error ' . $response_code . ':</strong> ' . esc_html($response_body) . '</p>';
                echo '</div>';
                return;
            }

            $data = json_decode($response_body, true);

            if (empty($data['records'])) {
                echo '<p><strong>⚠️ No records found</strong> - The query returned no results.</p>';
                echo '</div>';
                return;
            }

            $first_record = $data['records'][0];
            $all_fields = array_keys($first_record);

            // Remove Salesforce metadata fields
            $data_fields = array_filter($all_fields, function ($field) {
                return !in_array($field, ['attributes']);
            });

            echo '<p><strong>✅ Found ' . count($data['records']) . ' sample records</strong></p>';
            echo '<p><strong>📊 Total fields available:</strong> ' . count($data_fields) . '</p>';

            // Look for aircraft-related fields
            $aircraft_fields = array_filter($data_fields, function ($field) {
                return stripos($field, 'aircraft') !== false ||
                    stripos($field, 'helicopter') !== false ||
                    stripos($field, 'applicability') !== false ||
                    stripos($field, 'router') !== false ||
                    stripos($field, 'model') !== false ||
                    stripos($field, 'application') !== false;
            });

            if (!empty($aircraft_fields)) {
                echo '<h4>🚁 Aircraft-Related Fields Found:</h4>';
                echo '<ul>';
                foreach ($aircraft_fields as $field) {
                    $sample_values = [];
                    foreach ($data['records'] as $record) {
                        if (isset($record[$field]) && !empty($record[$field])) {
                            $sample_values[] = $record[$field];
                        }
                    }
                    echo '<li><strong>' . esc_html($field) . '</strong>';
                    if (!empty($sample_values)) {
                        echo ' - Sample values: <em>' . esc_html(implode(', ', array_unique($sample_values))) . '</em>';
                    } else {
                        echo ' - <em>No values in sample data</em>';
                    }
                    echo '</li>';
                }
                echo '</ul>';
            } else {
                echo '<p><strong>❌ No aircraft-related fields found</strong> in the current sync query.</p>';
            }

            // Show all available fields
            echo '<h4>📋 All Available Fields:</h4>';
            echo '<div style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f9f9f9;">';
            echo '<pre>' . esc_html(implode("\n", $data_fields)) . '</pre>';
            echo '</div>';

            // Show sample data for first record
            echo '<h4>📄 Sample Record Data:</h4>';
            echo '<div style="max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f9f9f9;">';
            echo '<pre>' . esc_html(json_encode($first_record, JSON_PRETTY_PRINT)) . '</pre>';
            echo '</div>';
        } catch (\Exception $e) {
            echo '<p><strong>❌ Exception:</strong> ' . esc_html($e->getMessage()) . '</p>';
        }

        echo '</div>';
    }

    /**
     * Debug Product object fields - shows what aircraft-related fields are available on the Product2 object
     */
    private static function debugProductFields($access_token)
    {
        echo '<div class="notice notice-info is-dismissible">';
        echo '<h3>🔍 Product Object Fields Debug</h3>';

        try {
            // First, get some Product IDs from inventory records
            $inventory_query = "SELECT inscor__Product__c FROM inscor__Inventory_Line__c WHERE inscor__Product__c != null LIMIT 5";
            $inventory_url = 'https://prod-avs-optimaaero--oasb.sandbox.my.salesforce.com/services/data/v55.0/query/?q=' . urlencode($inventory_query);

            $inventory_response = wp_remote_get($inventory_url, array(
                'timeout' => 30,
                'headers' => array(
                    'Authorization' => 'Bearer ' . $access_token,
                    'Accept' => 'application/json',
                ),
            ));

            if (is_wp_error($inventory_response)) {
                echo '<p><strong>❌ Error getting product IDs:</strong> ' . esc_html($inventory_response->get_error_message()) . '</p>';
                echo '</div>';
                return;
            }

            $inventory_data = json_decode(wp_remote_retrieve_body($inventory_response), true);
            if (empty($inventory_data['records'])) {
                echo '<p><strong>⚠️ No inventory records found</strong></p>';
                echo '</div>';
                return;
            }

            // Get the first product ID
            $product_id = $inventory_data['records'][0]['inscor__Product__c'];

            // Now query the Product2 object with all possible aircraft-related fields
            $product_query = "SELECT Id, Name, inscor__Master_Router_Applicability__c, inscor__Aircraft_Model__c, inscor__Application__c, inscor__Helicopter_Type__c, inscor__Aircraft_Type__c, inscor__Model__c, inscor__Applicability__c FROM Product2 WHERE Id = '" . $product_id . "'";

            $product_url = 'https://prod-avs-optimaaero--oasb.sandbox.my.salesforce.com/services/data/v55.0/query/?q=' . urlencode($product_query);

            $product_response = wp_remote_get($product_url, array(
                'timeout' => 30,
                'headers' => array(
                    'Authorization' => 'Bearer ' . $access_token,
                    'Accept' => 'application/json',
                ),
            ));

            if (is_wp_error($product_response)) {
                echo '<p><strong>❌ Error querying product:</strong> ' . esc_html($product_response->get_error_message()) . '</p>';
                echo '</div>';
                return;
            }

            $response_code = wp_remote_retrieve_response_code($product_response);
            $response_body = wp_remote_retrieve_body($product_response);

            if ($response_code !== 200) {
                echo '<p><strong>❌ HTTP Error ' . $response_code . ':</strong> ' . esc_html($response_body) . '</p>';
                echo '<p><strong>💡 This might mean some of the aircraft fields don\'t exist on the Product2 object.</strong></p>';

                // Try a simpler query to see what fields ARE available
                $simple_query = "SELECT Id, Name FROM Product2 WHERE Id = '" . $product_id . "'";
                $simple_url = 'https://prod-avs-optimaaero--oasb.sandbox.my.salesforce.com/services/data/v55.0/query/?q=' . urlencode($simple_query);

                $simple_response = wp_remote_get($simple_url, array(
                    'timeout' => 30,
                    'headers' => array(
                        'Authorization' => 'Bearer ' . $access_token,
                        'Accept' => 'application/json',
                    ),
                ));

                if (!is_wp_error($simple_response) && wp_remote_retrieve_response_code($simple_response) === 200) {
                    $simple_data = json_decode(wp_remote_retrieve_body($simple_response), true);
                    echo '<h4>✅ Basic Product Data Available:</h4>';
                    echo '<pre>' . esc_html(json_encode($simple_data, JSON_PRETTY_PRINT)) . '</pre>';
                    echo '<p><strong>🔍 Next Step:</strong> We need to describe the Product2 object to see what aircraft-related fields actually exist.</p>';
                }

                echo '</div>';
                return;
            }

            $product_data = json_decode($response_body, true);

            if (empty($product_data['records'])) {
                echo '<p><strong>⚠️ No product records found</strong></p>';
                echo '</div>';
                return;
            }

            $product_record = $product_data['records'][0];

            echo '<p><strong>✅ Found product record</strong></p>';
            echo '<p><strong>Product ID:</strong> ' . esc_html($product_id) . '</p>';
            echo '<p><strong>Product Name:</strong> ' . esc_html($product_record['Name']) . '</p>';

            // Look for aircraft-related fields with values
            $aircraft_fields_with_values = [];
            $aircraft_fields_empty = [];

            foreach ($product_record as $field => $value) {
                if (
                    stripos($field, 'aircraft') !== false ||
                    stripos($field, 'helicopter') !== false ||
                    stripos($field, 'applicability') !== false ||
                    stripos($field, 'router') !== false ||
                    stripos($field, 'model') !== false ||
                    stripos($field, 'application') !== false
                ) {

                    if (!empty($value) && $value !== null) {
                        $aircraft_fields_with_values[$field] = $value;
                    } else {
                        $aircraft_fields_empty[] = $field;
                    }
                }
            }

            if (!empty($aircraft_fields_with_values)) {
                echo '<h4>🚁 Aircraft Fields with Values:</h4>';
                echo '<ul>';
                foreach ($aircraft_fields_with_values as $field => $value) {
                    echo '<li><strong>' . esc_html($field) . '</strong>: <em>' . esc_html($value) . '</em></li>';
                }
                echo '</ul>';
            }

            if (!empty($aircraft_fields_empty)) {
                echo '<h4>📋 Aircraft Fields (Empty):</h4>';
                echo '<ul>';
                foreach ($aircraft_fields_empty as $field) {
                    echo '<li><strong>' . esc_html($field) . '</strong>: <em>No value</em></li>';
                }
                echo '</ul>';
            }

            if (empty($aircraft_fields_with_values) && empty($aircraft_fields_empty)) {
                echo '<p><strong>❌ No aircraft-related fields found</strong> in the Product2 object query.</p>';
            }

            // Show complete product record
            echo '<h4>📄 Complete Product Record:</h4>';
            echo '<div style="max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f9f9f9;">';
            echo '<pre>' . esc_html(json_encode($product_record, JSON_PRETTY_PRINT)) . '</pre>';
            echo '</div>';
        } catch (\Exception $e) {
            echo '<p><strong>❌ Exception:</strong> ' . esc_html($e->getMessage()) . '</p>';
        }

        echo '</div>';
    }

    /**
     * Describe Product2 object - shows ALL available fields on the Product2 object
     */
    private static function describeProduct2Object($access_token)
    {
        echo '<div class="notice notice-info is-dismissible">';
        echo '<h3>📋 Product2 Object Description</h3>';

        try {
            // Use Salesforce describe API to get all fields
            $describe_url = 'https://prod-avs-optimaaero--oasb.sandbox.my.salesforce.com/services/data/v55.0/sobjects/Product2/describe/';

            $response = wp_remote_get($describe_url, array(
                'timeout' => 30,
                'headers' => array(
                    'Authorization' => 'Bearer ' . $access_token,
                    'Accept' => 'application/json',
                ),
            ));

            if (is_wp_error($response)) {
                echo '<p><strong>❌ Error:</strong> ' . esc_html($response->get_error_message()) . '</p>';
                echo '</div>';
                return;
            }

            $response_code = wp_remote_retrieve_response_code($response);
            $response_body = wp_remote_retrieve_body($response);

            if ($response_code !== 200) {
                echo '<p><strong>❌ HTTP Error ' . $response_code . ':</strong> ' . esc_html($response_body) . '</p>';
                echo '</div>';
                return;
            }

            $describe_data = json_decode($response_body, true);

            if (empty($describe_data['fields'])) {
                echo '<p><strong>⚠️ No fields found</strong> in Product2 object description.</p>';
                echo '</div>';
                return;
            }

            $all_fields = $describe_data['fields'];
            $total_fields = count($all_fields);

            echo '<p><strong>✅ Found ' . $total_fields . ' total fields</strong> on the Product2 object</p>';

            // Look for aircraft-related fields
            $aircraft_fields = [];
            $custom_fields = [];
            $standard_fields = [];

            foreach ($all_fields as $field) {
                $field_name = $field['name'];
                $field_label = $field['label'];
                $field_type = $field['type'];
                $is_custom = $field['custom'];

                // Check if it's aircraft-related
                if (
                    stripos($field_name, 'aircraft') !== false ||
                    stripos($field_name, 'helicopter') !== false ||
                    stripos($field_name, 'applicability') !== false ||
                    stripos($field_name, 'router') !== false ||
                    stripos($field_name, 'model') !== false ||
                    stripos($field_name, 'application') !== false ||
                    stripos($field_label, 'aircraft') !== false ||
                    stripos($field_label, 'helicopter') !== false ||
                    stripos($field_label, 'applicability') !== false ||
                    stripos($field_label, 'router') !== false ||
                    stripos($field_label, 'model') !== false ||
                    stripos($field_label, 'application') !== false
                ) {

                    $aircraft_fields[] = [
                        'name' => $field_name,
                        'label' => $field_label,
                        'type' => $field_type,
                        'custom' => $is_custom
                    ];
                }

                // Categorize fields
                if ($is_custom) {
                    $custom_fields[] = [
                        'name' => $field_name,
                        'label' => $field_label,
                        'type' => $field_type
                    ];
                } else {
                    $standard_fields[] = [
                        'name' => $field_name,
                        'label' => $field_label,
                        'type' => $field_type
                    ];
                }
            }

            // Show aircraft-related fields
            if (!empty($aircraft_fields)) {
                echo '<h4>🚁 Aircraft-Related Fields Found (' . count($aircraft_fields) . '):</h4>';
                echo '<table class="widefat" style="margin-bottom: 15px;">';
                echo '<thead><tr><th>Field Name</th><th>Label</th><th>Type</th><th>Custom</th></tr></thead>';
                echo '<tbody>';
                foreach ($aircraft_fields as $field) {
                    $custom_badge = $field['custom'] ? '<span style="background: #0073aa; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px;">CUSTOM</span>' : '<span style="background: #666; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px;">STANDARD</span>';
                    echo '<tr>';
                    echo '<td><strong>' . esc_html($field['name']) . '</strong></td>';
                    echo '<td>' . esc_html($field['label']) . '</td>';
                    echo '<td>' . esc_html($field['type']) . '</td>';
                    echo '<td>' . $custom_badge . '</td>';
                    echo '</tr>';
                }
                echo '</tbody></table>';
            } else {
                echo '<p><strong>❌ No aircraft-related fields found</strong> on the Product2 object.</p>';
            }

            // Show summary of field types
            echo '<h4>📊 Field Summary:</h4>';
            echo '<ul>';
            echo '<li><strong>Total Fields:</strong> ' . $total_fields . '</li>';
            echo '<li><strong>Custom Fields:</strong> ' . count($custom_fields) . '</li>';
            echo '<li><strong>Standard Fields:</strong> ' . count($standard_fields) . '</li>';
            echo '<li><strong>Aircraft-Related:</strong> ' . count($aircraft_fields) . '</li>';
            echo '</ul>';

            // Show some custom fields for reference
            if (!empty($custom_fields)) {
                echo '<h4>🔧 Sample Custom Fields (first 10):</h4>';
                echo '<div style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f9f9f9;">';
                $sample_custom = array_slice($custom_fields, 0, 10);
                foreach ($sample_custom as $field) {
                    echo '<div><strong>' . esc_html($field['name']) . '</strong> - ' . esc_html($field['label']) . ' (' . esc_html($field['type']) . ')</div>';
                }
                if (count($custom_fields) > 10) {
                    echo '<div><em>... and ' . (count($custom_fields) - 10) . ' more custom fields</em></div>';
                }
                echo '</div>';
            }
        } catch (\Exception $e) {
            echo '<p><strong>❌ Exception:</strong> ' . esc_html($e->getMessage()) . '</p>';
        }

        echo '</div>';
    }

    /**
     * Test aircraft data with the new fields - shows what aircraft model data we get from the updated sync query
     */
    private static function testAircraftData($access_token)
    {
        echo '<div class="notice notice-info is-dismissible">';
        echo '<h3>🚁 Test Aircraft Data with New Fields</h3>';

        try {
            // Use the updated sync query with the new aircraft fields
            $query = "SELECT Id, Name, inscor__Product__c, inscor__Product__r.Name, inscor__Product__r.inscor__Applicability__c, inscor__Product__r.inscor__Applicability_Detailed__c, inscor__Repair_Type__c, inscor__Trace_Type__c, inscor__Public_Use_Aircraft__c FROM inscor__Inventory_Line__c WHERE inscor__Product__r.Name != null AND inscor__Quantity_Available__c > 0 AND inscor__Product__c != null LIMIT 10";

            $query_url = 'https://prod-avs-optimaaero--oasb.sandbox.my.salesforce.com/services/data/v55.0/query/?q=' . urlencode($query);

            $response = wp_remote_get($query_url, array(
                'timeout' => 30,
                'headers' => array(
                    'Authorization' => 'Bearer ' . $access_token,
                    'Accept' => 'application/json',
                ),
            ));

            if (is_wp_error($response)) {
                echo '<p><strong>❌ Error:</strong> ' . esc_html($response->get_error_message()) . '</p>';
                echo '</div>';
                return;
            }

            $response_code = wp_remote_retrieve_response_code($response);
            $response_body = wp_remote_retrieve_body($response);

            if ($response_code !== 200) {
                echo '<p><strong>❌ HTTP Error ' . $response_code . ':</strong> ' . esc_html($response_body) . '</p>';
                echo '</div>';
                return;
            }

            $data = json_decode($response_body, true);

            if (empty($data['records'])) {
                echo '<p><strong>⚠️ No records found</strong> - The query returned no results.</p>';
                echo '</div>';
                return;
            }

            echo '<p><strong>✅ Found ' . count($data['records']) . ' sample records</strong> with aircraft field data</p>';

            // Analyze aircraft data
            $records_with_applicability = 0;
            $records_with_detailed = 0;
            $unique_applicability_values = [];
            $unique_detailed_values = [];

            foreach ($data['records'] as $record) {
                // Check Applicability field
                if (isset($record['inscor__Product__r']['inscor__Applicability__c']) && !empty($record['inscor__Product__r']['inscor__Applicability__c'])) {
                    $records_with_applicability++;
                    $applicability = $record['inscor__Product__r']['inscor__Applicability__c'];
                    if (!in_array($applicability, $unique_applicability_values)) {
                        $unique_applicability_values[] = $applicability;
                    }
                }

                // Check Detailed Applicability field
                if (isset($record['inscor__Product__r']['inscor__Applicability_Detailed__c']) && !empty($record['inscor__Product__r']['inscor__Applicability_Detailed__c'])) {
                    $records_with_detailed++;
                    $detailed = $record['inscor__Product__r']['inscor__Applicability_Detailed__c'];
                    if (!in_array($detailed, $unique_detailed_values)) {
                        $unique_detailed_values[] = $detailed;
                    }
                }
            }

            // Show results
            echo '<h4>📊 Aircraft Data Analysis:</h4>';
            echo '<ul>';
            echo '<li><strong>Records with Applicability data:</strong> ' . $records_with_applicability . ' of ' . count($data['records']) . '</li>';
            echo '<li><strong>Records with Detailed Applicability data:</strong> ' . $records_with_detailed . ' of ' . count($data['records']) . '</li>';
            echo '<li><strong>Unique Applicability values:</strong> ' . count($unique_applicability_values) . '</li>';
            echo '<li><strong>Unique Detailed Applicability values:</strong> ' . count($unique_detailed_values) . '</li>';
            echo '</ul>';

            // Show sample applicability values
            if (!empty($unique_applicability_values)) {
                echo '<h4>🚁 Sample Applicability Values:</h4>';
                echo '<div style="background: #f0f8ff; border: 1px solid #0073aa; border-radius: 4px; padding: 10px; margin-bottom: 15px;">';
                foreach (array_slice($unique_applicability_values, 0, 10) as $value) {
                    echo '<div><strong>📋</strong> ' . esc_html($value) . '</div>';
                }
                if (count($unique_applicability_values) > 10) {
                    echo '<div><em>... and ' . (count($unique_applicability_values) - 10) . ' more values</em></div>';
                }
                echo '</div>';
            }

            // Show sample detailed applicability values
            if (!empty($unique_detailed_values)) {
                echo '<h4>🔍 Sample Detailed Applicability Values:</h4>';
                echo '<div style="background: #f0f8ff; border: 1px solid #0073aa; border-radius: 4px; padding: 10px; margin-bottom: 15px;">';
                foreach (array_slice($unique_detailed_values, 0, 10) as $value) {
                    echo '<div><strong>📋</strong> ' . esc_html($value) . '</div>';
                }
                if (count($unique_detailed_values) > 10) {
                    echo '<div><em>... and ' . (count($unique_detailed_values) - 10) . ' more values</em></div>';
                }
                echo '</div>';
            }

            // Show sample records
            echo '<h4>📄 Sample Records with Aircraft Data:</h4>';
            echo '<div style="max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f9f9f9;">';
            $sample_records = array_slice($data['records'], 0, 3);
            foreach ($sample_records as $i => $record) {
                echo '<div style="margin-bottom: 15px; padding: 10px; background: white; border-radius: 4px;">';
                echo '<strong>Record ' . ($i + 1) . ':</strong> ' . esc_html($record['inscor__Product__r']['Name']) . '<br>';

                if (isset($record['inscor__Product__r']['inscor__Applicability__c'])) {
                    echo '<strong>🚁 Applicability:</strong> ' . esc_html($record['inscor__Product__r']['inscor__Applicability__c']) . '<br>';
                }

                if (isset($record['inscor__Product__r']['inscor__Applicability_Detailed__c'])) {
                    echo '<strong>🔍 Detailed Applicability:</strong> ' . esc_html($record['inscor__Product__r']['inscor__Applicability_Detailed__c']) . '<br>';
                }
                echo '</div>';
            }
            echo '</div>';
        } catch (\Exception $e) {
            echo '<p><strong>❌ Exception:</strong> ' . esc_html($e->getMessage()) . '</p>';
        }

        echo '</div>';
    }

    /**
     * Debug WooCommerce aircraft attributes - shows what aircraft data is actually stored in WooCommerce
     */
    private static function debugWooCommerceAircraftData()
    {
        echo '<div class="notice notice-info is-dismissible">';
        echo '<h3>🔍 WooCommerce Aircraft Attributes Debug</h3>';

        try {
            // Get all products with aircraft attributes
            $products_query = new \WP_Query([
                'post_type' => 'product',
                'posts_per_page' => 50,
                'meta_query' => [
                    'relation' => 'OR',
                    [
                        'key' => '_product_attributes',
                        'value' => 'pa_application-code-aircraft',
                        'compare' => 'LIKE'
                    ],
                    [
                        'key' => '_product_attributes',
                        'value' => 'pa_aircraft-model',
                        'compare' => 'LIKE'
                    ]
                ]
            ]);

            echo '<p><strong>✅ Found ' . $products_query->found_posts . ' products</strong> with aircraft attributes</p>';

            if ($products_query->have_posts()) {
                // Analyze aircraft attribute data
                $aircraft_attribute_data = [];
                $products_with_aircraft = 0;

                while ($products_query->have_posts()) {
                    $products_query->the_post();
                    $product_id = get_the_ID();
                    $product = wc_get_product($product_id);

                    if ($product) {
                        $attributes = $product->get_attributes();
                        $has_aircraft_data = false;

                        foreach ($attributes as $attribute_name => $attribute) {
                            if (strpos($attribute_name, 'aircraft') !== false || strpos($attribute_name, 'application-code-aircraft') !== false) {
                                $has_aircraft_data = true;
                                $products_with_aircraft++;

                                // Get the terms for this attribute
                                if ($attribute->is_taxonomy()) {
                                    $terms = wp_get_post_terms($product_id, $attribute_name);
                                    $term_names = array_map(function ($term) {
                                        return $term->name;
                                    }, $terms);
                                    $aircraft_attribute_data[] = [
                                        'product_id' => $product_id,
                                        'product_name' => get_the_title(),
                                        'attribute_name' => $attribute_name,
                                        'terms' => $term_names
                                    ];
                                } else {
                                    $aircraft_attribute_data[] = [
                                        'product_id' => $product_id,
                                        'product_name' => get_the_title(),
                                        'attribute_name' => $attribute_name,
                                        'value' => $attribute->get_options()
                                    ];
                                }
                                break; // Only need one aircraft attribute per product for this debug
                            }
                        }
                    }
                }

                wp_reset_postdata();

                echo '<h4>📊 Aircraft Attribute Analysis:</h4>';
                echo '<ul>';
                echo '<li><strong>Products with aircraft attributes:</strong> ' . $products_with_aircraft . '</li>';
                echo '<li><strong>Total aircraft attribute entries:</strong> ' . count($aircraft_attribute_data) . '</li>';
                echo '</ul>';

                // Show unique aircraft values
                $unique_aircraft_values = [];
                foreach ($aircraft_attribute_data as $data) {
                    if (isset($data['terms'])) {
                        foreach ($data['terms'] as $term) {
                            if (!in_array($term, $unique_aircraft_values)) {
                                $unique_aircraft_values[] = $term;
                            }
                        }
                    } elseif (isset($data['value'])) {
                        foreach ($data['value'] as $value) {
                            if (!in_array($value, $unique_aircraft_values)) {
                                $unique_aircraft_values[] = $value;
                            }
                        }
                    }
                }

                echo '<h4>🚁 Unique Aircraft Values in WooCommerce:</h4>';
                echo '<div style="background: #f0f8ff; border: 1px solid #0073aa; border-radius: 4px; padding: 10px; margin-bottom: 15px;">';
                if (!empty($unique_aircraft_values)) {
                    foreach ($unique_aircraft_values as $value) {
                        echo '<div><strong>📋</strong> ' . esc_html($value) . '</div>';
                    }
                } else {
                    echo '<div><em>No aircraft values found</em></div>';
                }
                echo '</div>';

                // Show sample products
                echo '<h4>📄 Sample Products with Aircraft Data:</h4>';
                echo '<div style="max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f9f9f9;">';
                $sample_data = array_slice($aircraft_attribute_data, 0, 10);
                foreach ($sample_data as $data) {
                    echo '<div style="margin-bottom: 10px; padding: 8px; background: white; border-radius: 4px;">';
                    echo '<strong>Product:</strong> ' . esc_html($data['product_name']) . ' (ID: ' . $data['product_id'] . ')<br>';
                    echo '<strong>Attribute:</strong> ' . esc_html($data['attribute_name']) . '<br>';

                    if (isset($data['terms'])) {
                        echo '<strong>🚁 Aircraft Models:</strong> ' . esc_html(implode(', ', $data['terms'])) . '<br>';
                    } elseif (isset($data['value'])) {
                        echo '<strong>🚁 Aircraft Values:</strong> ' . esc_html(implode(', ', $data['value'])) . '<br>';
                    }
                    echo '</div>';
                }
                echo '</div>';
            } else {
                echo '<p><strong>❌ No products found</strong> with aircraft attributes.</p>';

                // Check if any aircraft attributes exist at all
                $aircraft_attributes = wc_get_attribute_taxonomies();
                $found_aircraft_attrs = [];
                foreach ($aircraft_attributes as $attr) {
                    if (strpos($attr->attribute_name, 'aircraft') !== false || strpos($attr->attribute_name, 'application') !== false) {
                        $found_aircraft_attrs[] = $attr;
                    }
                }

                if (!empty($found_aircraft_attrs)) {
                    echo '<h4>🔍 Found Aircraft Attribute Taxonomies:</h4>';
                    foreach ($found_aircraft_attrs as $attr) {
                        echo '<div><strong>' . esc_html($attr->attribute_name) . '</strong> - ' . esc_html($attr->attribute_label) . '</div>';
                    }
                } else {
                    echo '<p><strong>❌ No aircraft attribute taxonomies found</strong> in WooCommerce.</p>';
                }
            }
        } catch (\Exception $e) {
            echo '<p><strong>❌ Exception:</strong> ' . esc_html($e->getMessage()) . '</p>';
        }

        echo '</div>';
    }

    /**
     * Render the admin page
     */
    public static function renderAdminPage()
    {
        // Handle form submissions
        self::handleFormSubmissions();

        // Clear old sync errors if no recent sync activity
        self::clearOldErrors();

        echo '<div class="wrap">';
        echo '<h1>🔄 Avsight Inventory Sync</h1>';
        echo '<p>Synchronize WooCommerce inventory with Avsight Salesforce data using bulk operations for optimal performance.</p>';

        // Render tabs
        self::renderTabs();

        echo '</div>';
    }

    /**
     * Handle form submissions
     */
    private static function handleFormSubmissions()
    {
        // Handle API connection test
        if (isset($_POST['avsight_test_api_nonce']) && wp_verify_nonce(sanitize_key(wp_unslash($_POST['avsight_test_api_nonce'])), 'avsight_test_api_connection')) {
            if (current_user_can('manage_options')) {
                // Clear any existing sync errors before testing API
                delete_transient('avsight_sync_error');

                $test_token = AvsightApi::getAccessToken();
                if (is_wp_error($test_token)) {
                    $error_messages = $test_token->get_error_messages();
                    $error_output = implode(' ', $error_messages);
                    echo '<div class="notice notice-error is-dismissible"><p><strong>API Connection Test Failed:</strong> ' . esc_html(trim($error_output)) . '. Please check your Salesforce credentials in the .env file and ensure your Salesforce Connected App is correctly configured.</p></div>';
                } else {
                    echo '<div class="notice notice-success is-dismissible"><p><strong>API Connection Test Successful!</strong> Access token retrieved. You can now proceed with syncing.</p></div>';
                }
            } else {
                echo '<div class="notice notice-error"><p>You do not have sufficient permissions to test the API connection.</p></div>';
            }
        }

        // Handle manual sync trigger
        if (isset($_POST['avsight_sync_nonce']) && wp_verify_nonce(sanitize_key(wp_unslash($_POST['avsight_sync_nonce'])), 'avsight_manual_sync')) {
            if (current_user_can('manage_options')) {
                // Clear any existing scheduled batches before starting a new full sync
                wp_clear_scheduled_hook('avsight_process_inventory_batch_event');
                // Clear old transient and any lingering temp files from previous incomplete runs
                delete_transient(AVSIGHT_SYNC_TRANSIENT_KEY);
                delete_transient('avsight_sync_error'); // Clear any previous sync errors

                AvsightSyncOperations::runInventorySync();
                echo '<div class="notice notice-success is-dismissible"><p>Avsight Inventory Sync Initiated (Batch Process). Check error logs for details.</p></div>';
            } else {
                echo '<div class="notice notice-error"><p>You do not have sufficient permissions to run the sync.</p></div>';
            }
        }

        // Handle bulk delete trigger
        if (isset($_POST['avsight_bulk_delete_nonce']) && wp_verify_nonce(sanitize_key(wp_unslash($_POST['avsight_bulk_delete_nonce'])), 'avsight_bulk_delete_products')) {
            if (current_user_can('manage_options')) {
                $result = AvsightDeleteOperations::initBulkDelete();
                if ($result) {
                    echo '<div class="notice notice-success is-dismissible"><p>Bulk product deletion initiated. The process will run in the background.</p></div>';
                } else {
                    echo '<div class="notice notice-error is-dismissible"><p>Failed to initiate bulk product deletion. Check error logs for details.</p></div>';
                }
            } else {
                echo '<div class="notice notice-error"><p>You do not have sufficient permissions to delete products.</p></div>';
            }
        }

        // Handle force daily sync trigger
        if (isset($_POST['avsight_force_sync_nonce']) && wp_verify_nonce(sanitize_key(wp_unslash($_POST['avsight_force_sync_nonce'])), 'avsight_force_daily_sync')) {
            if (current_user_can('manage_options')) {
                AvsightScheduler::forceRunDailySync();
                echo '<div class="notice notice-success is-dismissible"><p>Daily sync manually triggered. Check the sync status for progress.</p></div>';
            } else {
                echo '<div class="notice notice-error"><p>You do not have sufficient permissions to trigger sync.</p></div>';
            }
        }

        // Handle API test with standard objects
        if (isset($_POST['avsight_test_api_access_nonce']) && wp_verify_nonce(sanitize_key(wp_unslash($_POST['avsight_test_api_access_nonce'])), 'avsight_test_api_access')) {
            if (current_user_can('manage_options')) {
                $access_token = AvsightApi::getAccessToken();
                if (is_wp_error($access_token)) {
                    echo '<div class="notice notice-error is-dismissible"><p><strong>API Access Test Failed:</strong> Could not get access token - ' . esc_html($access_token->get_error_message()) . '</p></div>';
                } else {
                    $test_result = AvsightApi::testApiAccess($access_token);
                    if (is_wp_error($test_result)) {
                        echo '<div class="notice notice-error is-dismissible"><p><strong>API Access Test Failed:</strong> ' . esc_html($test_result->get_error_message()) . '</p></div>';
                    } else {
                        if ($test_result['success']) {
                            $job_id = $test_result['job_data']['id'] ?? 'Unknown';
                            $job_state = $test_result['job_data']['state'] ?? 'Unknown';
                            echo '<div class="notice notice-success is-dismissible"><p><strong>✅ Bulk API Test Successful!</strong> Job created successfully.</p>';
                            echo '<p><strong>Job ID:</strong> ' . esc_html($job_id) . '</p>';
                            echo '<p><strong>Job State:</strong> ' . esc_html($job_state) . '</p>';
                            echo '<p><strong>Object:</strong> AcctSeed__Inventory_Cost__c</p>';
                            echo '<p><strong>Format:</strong> CSV</p></div>';
                        } else {
                            echo '<div class="notice notice-error is-dismissible"><p><strong>API Access Test Failed:</strong> HTTP ' . $test_result['response_code'] . ' - ' . esc_html($test_result['response_body']) . '</p></div>';
                        }
                    }
                }
            }
        }

        // Handle list available objects
        if (isset($_POST['avsight_list_objects_nonce']) && wp_verify_nonce(sanitize_key(wp_unslash($_POST['avsight_list_objects_nonce'])), 'avsight_list_objects')) {
            if (current_user_can('manage_options')) {
                $access_token = AvsightApi::getAccessToken();
                if (is_wp_error($access_token)) {
                    echo '<div class="notice notice-error is-dismissible"><p><strong>Object List Failed:</strong> Could not get access token - ' . esc_html($access_token->get_error_message()) . '</p></div>';
                } else {
                    $objects_result = AvsightApi::listAvailableObjects($access_token);
                    if (is_wp_error($objects_result)) {
                        echo '<div class="notice notice-error is-dismissible"><p><strong>Object List Failed:</strong> ' . esc_html($objects_result->get_error_message()) . '</p></div>';
                    } else {
                        echo '<div class="notice notice-info is-dismissible">';
                        echo '<p><strong>Available Inventory-Related Objects:</strong></p>';
                        if (!empty($objects_result['relevant_objects'])) {
                            echo '<ul>';
                            foreach ($objects_result['relevant_objects'] as $obj) {
                                echo '<li><strong>' . esc_html($obj['name']) . '</strong> - ' . esc_html($obj['label']) . ' (Custom: ' . ($obj['custom'] ? 'Yes' : 'No') . ')</li>';
                            }
                            echo '</ul>';
                        } else {
                            echo '<p>No inventory-related objects found. Check error logs for full object list.</p>';
                        }
                        echo '</div>';
                    }
                }
            }
        }

        // Handle aircraft fields discovery
        if (isset($_POST['avsight_discover_aircraft_nonce']) && wp_verify_nonce(sanitize_key(wp_unslash($_POST['avsight_discover_aircraft_nonce'])), 'avsight_discover_aircraft_fields')) {
            if (current_user_can('manage_options')) {
                $access_token = AvsightApi::getAccessToken();
                if (is_wp_error($access_token)) {
                    echo '<div class="notice notice-error is-dismissible"><p><strong>Aircraft Fields Discovery Failed:</strong> Could not get access token - ' . esc_html($access_token->get_error_message()) . '</p></div>';
                } else {
                    $aircraft_fields = AvsightApi::discoverAircraftFields($access_token);
                    if (is_wp_error($aircraft_fields)) {
                        echo '<div class="notice notice-error is-dismissible"><p><strong>Aircraft Fields Discovery Failed:</strong> ' . esc_html($aircraft_fields->get_error_message()) . '</p></div>';
                    } else {
                        echo '<div class="notice notice-info is-dismissible">';
                        echo '<p><strong>🚁 Aircraft/Helicopter Related Fields Found:</strong></p>';
                        if (!empty($aircraft_fields)) {
                            echo '<ul>';
                            foreach ($aircraft_fields as $field) {
                                echo '<li><strong>' . esc_html($field['object']) . '.' . esc_html($field['field_name']) . '</strong> - ' . esc_html($field['field_label']) . ' (' . esc_html($field['field_type']) . ')</li>';
                            }
                            echo '</ul>';
                            echo '<p><strong>💡 Next Steps:</strong> If any of these fields contain aircraft model data (like AS350B2, Bell 212), we can add them to the sync query for WooCommerce product attributes.</p>';
                        } else {
                            echo '<p>❌ No aircraft/helicopter related fields found in the available objects. The aircraft model data might be stored in a different object or with different field names.</p>';
                            echo '<p><strong>💡 Suggestion:</strong> Try checking the Product object fields or look for fields containing "application", "model", or "type" in their names.</p>';
                        }
                        echo '</div>';
                    }
                }
            }
        }

        // Handle aircraft field values sampling
        if (isset($_POST['avsight_sample_aircraft_nonce']) && wp_verify_nonce(sanitize_key(wp_unslash($_POST['avsight_sample_aircraft_nonce'])), 'avsight_sample_aircraft_values')) {
            if (current_user_can('manage_options')) {
                $access_token = AvsightApi::getAccessToken();
                if (is_wp_error($access_token)) {
                    echo '<div class="notice notice-error is-dismissible"><p><strong>Aircraft Values Sampling Failed:</strong> Could not get access token - ' . esc_html($access_token->get_error_message()) . '</p></div>';
                } else {
                    $sample_values = AvsightApi::sampleAircraftFieldValues($access_token);
                    if (is_wp_error($sample_values)) {
                        echo '<div class="notice notice-error is-dismissible"><p><strong>Aircraft Values Sampling Failed:</strong> ' . esc_html($sample_values->get_error_message()) . '</p></div>';
                    } else {
                        echo '<div class="notice notice-info is-dismissible">';
                        echo '<p><strong>📊 Sample Aircraft Field Values (first 10 records):</strong></p>';
                        if (!empty($sample_values)) {
                            echo '<table style="border-collapse: collapse; width: 100%;">';
                            echo '<tr style="background: #f5f5f5;">';
                            foreach (array_keys($sample_values[0]) as $header) {
                                if ($header !== 'Id') {
                                    echo '<th style="border: 1px solid #ddd; padding: 8px; text-align: left;">' . esc_html($header) . '</th>';
                                }
                            }
                            echo '</tr>';

                            foreach ($sample_values as $row) {
                                echo '<tr>';
                                foreach ($row as $field => $value) {
                                    if ($field !== 'Id') {
                                        echo '<td style="border: 1px solid #ddd; padding: 8px;">' . esc_html($value ?: '(empty)') . '</td>';
                                    }
                                }
                                echo '</tr>';
                            }
                            echo '</table>';
                            echo '<p><strong>💡 Analysis:</strong> Look for values that contain aircraft models like AS350B2, Bell 212, AW139, etc. These fields can be used for WooCommerce product attributes.</p>';
                        } else {
                            echo '<p>❌ No sample data returned. Check if there are inventory records with available quantities.</p>';
                        }
                        echo '</div>';
                    }
                }
            }
        }

        // Handle debug current sync fields
        if (isset($_POST['avsight_debug_sync_fields_nonce']) && wp_verify_nonce(sanitize_key(wp_unslash($_POST['avsight_debug_sync_fields_nonce'])), 'avsight_debug_sync_fields')) {
            if (current_user_can('manage_options')) {
                $access_token = AvsightApi::getAccessToken();
                if (is_wp_error($access_token)) {
                    echo '<div class="notice notice-error is-dismissible"><p><strong>Debug Sync Fields Failed:</strong> Could not get access token - ' . esc_html($access_token->get_error_message()) . '</p></div>';
                } else {
                    self::debugCurrentSyncFields($access_token);
                }
            }
        }

        // Handle debug product fields
        if (isset($_POST['avsight_debug_product_fields_nonce']) && wp_verify_nonce(sanitize_key(wp_unslash($_POST['avsight_debug_product_fields_nonce'])), 'avsight_debug_product_fields')) {
            if (current_user_can('manage_options')) {
                $access_token = AvsightApi::getAccessToken();
                if (is_wp_error($access_token)) {
                    echo '<div class="notice notice-error is-dismissible"><p><strong>Debug Product Fields Failed:</strong> Could not get access token - ' . esc_html($access_token->get_error_message()) . '</p></div>';
                } else {
                    self::debugProductFields($access_token);
                }
            }
        }

        // Handle describe Product2 object
        if (isset($_POST['avsight_describe_product2_nonce']) && wp_verify_nonce(sanitize_key(wp_unslash($_POST['avsight_describe_product2_nonce'])), 'avsight_describe_product2')) {
            if (current_user_can('manage_options')) {
                $access_token = AvsightApi::getAccessToken();
                if (is_wp_error($access_token)) {
                    echo '<div class="notice notice-error is-dismissible"><p><strong>Describe Product2 Failed:</strong> Could not get access token - ' . esc_html($access_token->get_error_message()) . '</p></div>';
                } else {
                    self::describeProduct2Object($access_token);
                }
            }
        }

        // Handle test aircraft fields
        if (isset($_POST['avsight_test_aircraft_data_nonce']) && wp_verify_nonce(sanitize_key(wp_unslash($_POST['avsight_test_aircraft_data_nonce'])), 'avsight_test_aircraft_data')) {
            if (current_user_can('manage_options')) {
                $access_token = AvsightApi::getAccessToken();
                if (is_wp_error($access_token)) {
                    echo '<div class="notice notice-error is-dismissible"><p><strong>Test Aircraft Data Failed:</strong> Could not get access token - ' . esc_html($access_token->get_error_message()) . '</p></div>';
                } else {
                    self::testAircraftData($access_token);
                }
            }
        }

        // Handle debug WooCommerce aircraft attributes
        if (isset($_POST['avsight_debug_wc_aircraft_nonce']) && wp_verify_nonce(sanitize_key(wp_unslash($_POST['avsight_debug_wc_aircraft_nonce'])), 'avsight_debug_wc_aircraft')) {
            if (current_user_can('manage_options')) {
                self::debugWooCommerceAircraftData();
            }
        }

        // Handle application code mappings
        if (isset($_POST['avsight_save_app_mappings_nonce']) && wp_verify_nonce(sanitize_key(wp_unslash($_POST['avsight_save_app_mappings_nonce'])), 'avsight_save_app_mappings')) {
            if (current_user_can('manage_options')) {
                self::saveApplicationCodeMappings();
            }
        }

        // Handle condition code mappings
        if (isset($_POST['avsight_save_condition_mappings_nonce']) && wp_verify_nonce(sanitize_key(wp_unslash($_POST['avsight_save_condition_mappings_nonce'])), 'avsight_save_condition_mappings')) {
            if (current_user_can('manage_options')) {
                self::saveConditionCodeMappings();
            }
        }

        // Handle product merging rules
        if (isset($_POST['avsight_save_merging_rules_nonce']) && wp_verify_nonce(sanitize_key(wp_unslash($_POST['avsight_save_merging_rules_nonce'])), 'avsight_save_merging_rules')) {
            if (current_user_can('manage_options')) {
                self::saveProductMergingRules();
            }
        }

        // Handle describe inventory object
        if (isset($_POST['avsight_describe_object_nonce']) && wp_verify_nonce(sanitize_key(wp_unslash($_POST['avsight_describe_object_nonce'])), 'avsight_describe_object')) {
            if (current_user_can('manage_options')) {
                $access_token = AvsightApi::getAccessToken();
                if (is_wp_error($access_token)) {
                    echo '<div class="notice notice-error is-dismissible"><p><strong>Object Description Failed:</strong> Could not get access token - ' . esc_html($access_token->get_error_message()) . '</p></div>';
                } else {
                    // Try to describe the correct inventory object
                    $object_result = AvsightApi::describeObject($access_token, 'inscor__Inventory_Line__c');
                    if (is_wp_error($object_result)) {
                        echo '<div class="notice notice-error is-dismissible"><p><strong>Object Description Failed:</strong> ' . esc_html($object_result->get_error_message()) . '</p></div>';
                    } else {
                        echo '<div class="notice notice-info is-dismissible">';
                        echo '<p><strong>Fields in inscor__Inventory_Line__c:</strong></p>';
                        if (!empty($object_result['relevant_fields'])) {
                            echo '<h4>Relevant Fields:</h4>';
                            echo '<ul>';
                            foreach ($object_result['relevant_fields'] as $field) {
                                echo '<li><strong>' . esc_html($field['name']) . '</strong> - ' . esc_html($field['label']) . ' (' . esc_html($field['type']) . ')</li>';
                            }
                            echo '</ul>';
                        } else {
                            echo '<p>No relevant inventory fields found with current filters.</p>';
                        }

                        // Show fields that might be relevant for product merging
                        if (!empty($object_result['all_fields'])) {
                            // Filter for fields that might contain the data we need
                            $product_fields = array_filter($object_result['all_fields'], function ($field) {
                                $name_lower = strtolower($field['name']);
                                $label_lower = strtolower($field['label']);
                                return (
                                    // Part number related
                                    stripos($name_lower, 'part') !== false ||
                                    stripos($label_lower, 'part') !== false ||
                                    stripos($name_lower, 'product') !== false ||
                                    stripos($label_lower, 'product') !== false ||
                                    // Description related
                                    stripos($name_lower, 'description') !== false ||
                                    stripos($label_lower, 'description') !== false ||
                                    stripos($name_lower, 'name') !== false ||
                                    stripos($label_lower, 'name') !== false ||
                                    // Condition related
                                    stripos($name_lower, 'condition') !== false ||
                                    stripos($label_lower, 'condition') !== false ||
                                    // Certification related
                                    stripos($name_lower, 'cert') !== false ||
                                    stripos($label_lower, 'cert') !== false ||
                                    stripos($name_lower, 'form') !== false ||
                                    stripos($label_lower, 'form') !== false ||
                                    // Warehouse related
                                    stripos($name_lower, 'warehouse') !== false ||
                                    stripos($label_lower, 'warehouse') !== false ||
                                    stripos($name_lower, 'location') !== false ||
                                    stripos($label_lower, 'location') !== false ||
                                    // Serial number related
                                    stripos($name_lower, 'serial') !== false ||
                                    stripos($label_lower, 'serial') !== false ||
                                    // Helicopter type related
                                    stripos($name_lower, 'helicopter') !== false ||
                                    stripos($label_lower, 'helicopter') !== false ||
                                    stripos($name_lower, 'aircraft') !== false ||
                                    stripos($label_lower, 'aircraft') !== false ||
                                    // Quantity related
                                    stripos($name_lower, 'quantity') !== false ||
                                    stripos($label_lower, 'quantity') !== false ||
                                    stripos($name_lower, 'available') !== false ||
                                    stripos($label_lower, 'available') !== false ||
                                    // Cost related
                                    stripos($name_lower, 'cost') !== false ||
                                    stripos($label_lower, 'cost') !== false ||
                                    stripos($name_lower, 'price') !== false ||
                                    stripos($label_lower, 'price') !== false
                                );
                            });

                            if (!empty($product_fields)) {
                                echo '<h4>🎯 Product-Related Fields (' . count($product_fields) . ' found):</h4>';
                                echo '<ul>';
                                foreach ($product_fields as $field) {
                                    echo '<li><strong>' . esc_html($field['name']) . '</strong> - ' . esc_html($field['label']) . ' (' . esc_html($field['type']) . ')</li>';
                                }
                                echo '</ul>';
                            }

                            echo '<h4>All Fields (first 50):</h4>';
                            echo '<ul>';
                            $count = 0;
                            foreach ($object_result['all_fields'] as $field) {
                                if ($count >= 50) break;
                                echo '<li><strong>' . esc_html($field['name']) . '</strong> - ' . esc_html($field['label']) . ' (' . esc_html($field['type']) . ')</li>';
                                $count++;
                            }
                            echo '</ul>';
                            echo '<p><em>Showing first 50 of ' . count($object_result['all_fields']) . ' total fields. Check error logs for complete field list.</em></p>';
                        }
                        echo '</div>';
                    }
                }
            }
        }

        // Handle clear error cache
        if (isset($_POST['avsight_clear_cache_nonce']) && wp_verify_nonce(sanitize_key(wp_unslash($_POST['avsight_clear_cache_nonce'])), 'avsight_clear_cache')) {
            if (current_user_can('manage_options')) {
                // Clear all sync-related transients
                delete_transient('avsight_sync_error');
                delete_transient('avsight_delete_error');
                delete_transient(AVSIGHT_SYNC_TRANSIENT_KEY);
                delete_transient(AVSIGHT_SYNC_TOTAL_ITEMS_KEY);
                delete_transient(AVSIGHT_SYNC_PROCESSED_ITEMS_KEY);
                delete_transient(AVSIGHT_SYNC_LAST_RUN_START_KEY);
                delete_transient(AVSIGHT_SYNC_LAST_RUN_END_KEY);

                echo '<div class="notice notice-success is-dismissible"><p><strong>✅ Cache Cleared!</strong> All cached errors and sync status have been cleared.</p></div>';
            }
        }

        // Handle removal settings update
        if (isset($_POST['avsight_removal_nonce']) && wp_verify_nonce(sanitize_key(wp_unslash($_POST['avsight_removal_nonce'])), 'avsight_removal_settings')) {
            if (current_user_can('manage_options')) {
                $removal_action = isset($_POST['removal_action']) ? sanitize_text_field($_POST['removal_action']) : 'out_of_stock';

                // Validate removal action
                $valid_actions = ['out_of_stock', 'disable', 'delete'];
                if (in_array($removal_action, $valid_actions)) {
                    update_option('avsight_removal_action', $removal_action);

                    $action_labels = [
                        'out_of_stock' => 'Set out of stock',
                        'disable' => 'Disable products',
                        'delete' => 'Delete products'
                    ];

                    echo '<div class="notice notice-success is-dismissible"><p><strong>✅ Removal Settings Saved!</strong> Products no longer in Avsight will be: ' . esc_html($action_labels[$removal_action]) . '</p></div>';
                } else {
                    echo '<div class="notice notice-error is-dismissible"><p><strong>❌ Invalid removal action selected.</strong></p></div>';
                }
            } else {
                echo '<div class="notice notice-error"><p>You do not have sufficient permissions to update removal settings.</p></div>';
            }
        }

        // Handle incremental sync settings update
        if (isset($_POST['avsight_incremental_nonce']) && wp_verify_nonce(sanitize_key(wp_unslash($_POST['avsight_incremental_nonce'])), 'avsight_incremental_settings')) {
            if (current_user_can('manage_options')) {
                $incremental_enabled = isset($_POST['incremental_enabled']) ? true : false;

                // Debug logging
                error_log('Avsight Admin Debug: Form submitted - incremental_enabled checkbox: ' . (isset($_POST['incremental_enabled']) ? 'CHECKED' : 'UNCHECKED'));
                error_log('Avsight Admin Debug: Setting incremental_enabled to: ' . ($incremental_enabled ? 'TRUE' : 'FALSE'));

                $update_result = update_option('avsight_incremental_sync_enabled', $incremental_enabled);
                error_log('Avsight Admin Debug: update_option result: ' . ($update_result ? 'SUCCESS' : 'FAILED'));

                // If incremental sync is disabled, auto-clear the sync history
                if (!$incremental_enabled) {
                    delete_option('avsight_last_successful_sync');
                    error_log('Avsight Admin Debug: Auto-cleared sync history because incremental sync was disabled');
                }

                // Verify the setting was saved
                $saved_value = get_option('avsight_incremental_sync_enabled', 'NOT_SET');
                error_log('Avsight Admin Debug: Verified saved value: ' . ($saved_value === 'NOT_SET' ? 'NOT_SET' : ($saved_value ? 'TRUE' : 'FALSE')));

                $status_text = $incremental_enabled ? 'enabled' : 'disabled';
                $extra_message = !$incremental_enabled ? ' Sync history has been automatically cleared.' : '';
                echo '<div class="notice notice-success is-dismissible"><p><strong>✅ Incremental Sync Settings Saved!</strong> Incremental sync is now ' . esc_html($status_text) . '.' . esc_html($extra_message) . '</p></div>';
            } else {
                echo '<div class="notice notice-error"><p>You do not have sufficient permissions to update incremental sync settings.</p></div>';
            }
        }

        // Handle sync timestamp reset
        if (isset($_POST['avsight_reset_timestamp_nonce']) && wp_verify_nonce(sanitize_key(wp_unslash($_POST['avsight_reset_timestamp_nonce'])), 'avsight_reset_sync_timestamp')) {
            if (current_user_can('manage_options')) {
                delete_option('avsight_last_successful_sync');
                echo '<div class="notice notice-success is-dismissible"><p><strong>✅ Sync History Reset!</strong> The next sync will be a full sync processing all products.</p></div>';
            } else {
                echo '<div class="notice notice-error"><p>You do not have sufficient permissions to reset sync history.</p></div>';
            }
        }

        // Handle performance settings update
        if (isset($_POST['avsight_performance_nonce']) && wp_verify_nonce(sanitize_key(wp_unslash($_POST['avsight_performance_nonce'])), 'avsight_performance_settings')) {
            if (current_user_can('manage_options')) {
                $sync_batch_size = isset($_POST['sync_batch_size']) ? (int) $_POST['sync_batch_size'] : AVSIGHT_SYNC_BATCH_SIZE;
                $delete_batch_size = isset($_POST['delete_batch_size']) ? (int) $_POST['delete_batch_size'] : AVSIGHT_BULK_DELETE_BATCH_SIZE;
                $sync_limit = isset($_POST['sync_limit']) ? (int) $_POST['sync_limit'] : 0;
                $debug_mode = isset($_POST['debug_mode']) ? true : false;
                $safe_mode = isset($_POST['safe_mode_limited_sync']) ? true : false;

                // Validate batch sizes
                if ($sync_batch_size < 1 || $sync_batch_size > 200) {
                    $sync_batch_size = AVSIGHT_SYNC_BATCH_SIZE;
                }
                if ($delete_batch_size < 1 || $delete_batch_size > 200) {
                    $delete_batch_size = AVSIGHT_BULK_DELETE_BATCH_SIZE;
                }

                // Validate sync limit
                if ($sync_limit < 0 || $sync_limit > 10000) {
                    $sync_limit = 0;
                }

                // If debug mode is disabled, force sync limit to 0
                if (!$debug_mode) {
                    $sync_limit = 0;
                }

                // Save settings
                update_option('avsight_sync_batch_size', $sync_batch_size);
                update_option('avsight_delete_batch_size', $delete_batch_size);
                update_option('avsight_sync_limit', $sync_limit);
                update_option('avsight_debug_mode', $debug_mode);
                update_option('avsight_safe_mode_limited_sync', $safe_mode);

                $debug_status = $debug_mode ? 'enabled' : 'disabled';
                $limit_text = ($debug_mode && $sync_limit > 0) ? " (sync limit: {$sync_limit})" : '';

                echo '<div class="notice notice-success is-dismissible"><p><strong>✅ Performance Settings Saved!</strong> Sync batch: ' . $sync_batch_size . ', Delete batch: ' . $delete_batch_size . ', Debug mode: ' . $debug_status . $limit_text . '</p></div>';
            } else {
                echo '<div class="notice notice-error"><p>You do not have sufficient permissions to update performance settings.</p></div>';
            }
        }

        // Handle emergency sync stop
        if (isset($_POST['avsight_emergency_stop_nonce']) && wp_verify_nonce(sanitize_key(wp_unslash($_POST['avsight_emergency_stop_nonce'])), 'avsight_emergency_stop_sync')) {
            if (current_user_can('manage_options')) {
                // Clear ALL scheduled events related to sync
                wp_clear_scheduled_hook('avsight_process_inventory_batch_event');
                wp_clear_scheduled_hook('avsight_monitor_bulk_query_event');

                // Clear ALL sync transients
                delete_transient(AVSIGHT_SYNC_TRANSIENT_KEY);
                delete_transient('avsight_bulk_query_job_id');
                delete_transient('avsight_bulk_query_access_token');
                delete_transient(AVSIGHT_SYNC_TOTAL_ITEMS_KEY);
                delete_transient(AVSIGHT_SYNC_PROCESSED_ITEMS_KEY);
                delete_transient(AVSIGHT_SYNC_LAST_RUN_START_KEY);
                delete_transient(AVSIGHT_SYNC_LAST_RUN_END_KEY);
                delete_transient('avsight_sync_error');
                delete_transient('avsight_sync_log_messages');

                // Clear temporary files
                $temp_files = [
                    AVSIGHT_SYNC_DATA_FILE_PATH_KEY,
                    AVSIGHT_ALL_PRODUCT_IDS_FILE_PATH_KEY,
                    AVSIGHT_SYNCED_PRODUCT_IDS_FILE_PATH_KEY,
                    AVSIGHT_UNSYNCED_IDS_FILE_PATH_KEY
                ];

                foreach ($temp_files as $file_key) {
                    $file_path = get_transient($file_key);
                    if ($file_path && file_exists($file_path)) {
                        unlink($file_path);
                    }
                    delete_transient($file_key);
                }

                // Clear offset transients
                delete_transient(AVSIGHT_SYNC_DATA_FILE_OFFSET_KEY);
                delete_transient(AVSIGHT_UNSYNCED_IDS_FILE_OFFSET_KEY);

                error_log('Avsight Sync: Emergency stop executed - all sync processes, transients, and files cleared.');

                echo '<div class="notice notice-success is-dismissible"><p><strong>🛑 Emergency Stop Completed!</strong> All sync processes have been stopped and progress has been reset. You can now start a fresh sync.</p></div>';
            } else {
                echo '<div class="notice notice-error"><p>You do not have sufficient permissions to stop sync processes.</p></div>';
            }
        }

        // Handle emergency product restore
        if (isset($_POST['avsight_emergency_restore_nonce']) && wp_verify_nonce(sanitize_key(wp_unslash($_POST['avsight_emergency_restore_nonce'])), 'avsight_emergency_restore')) {
            if (current_user_can('manage_options')) {
                $restored_count = AvsightSyncOperations::emergencyRestoreProducts();
                if ($restored_count > 0) {
                    echo '<div class="notice notice-success is-dismissible"><p><strong>✅ Emergency Restore Completed!</strong> Restored ' . $restored_count . ' products back to in-stock status.</p></div>';
                } else {
                    echo '<div class="notice notice-info is-dismissible"><p><strong>ℹ️ No Products to Restore:</strong> All Avsight products are already in-stock or no out-of-stock products were found.</p></div>';
                }
            } else {
                echo '<div class="notice notice-error"><p>You do not have sufficient permissions to restore products.</p></div>';
            }
        }
    }

    /**
     * Clear old sync errors if no recent sync activity
     */
    private static function clearOldErrors()
    {
        $last_run_start = get_transient(AVSIGHT_SYNC_LAST_RUN_START_KEY);
        $last_run_end = get_transient(AVSIGHT_SYNC_LAST_RUN_END_KEY);
        $sync_error = get_transient('avsight_sync_error');

        // If there's an error but no recent sync activity (older than 1 hour), clear it
        if ($sync_error && (!$last_run_start || ($last_run_end && (time() - $last_run_end) > HOUR_IN_SECONDS))) {
            delete_transient('avsight_sync_error');
        }

        // Clear stale data file transients that cause "Data file not found" errors
        if (!get_transient(AVSIGHT_SYNC_TRANSIENT_KEY)) {
            // No sync running, clear any stale file references
            delete_transient(AVSIGHT_SYNC_DATA_FILE_PATH_KEY);
            delete_transient(AVSIGHT_SYNC_DATA_FILE_OFFSET_KEY);
        }
    }

    /**
     * Render admin tabs
     */
    private static function renderTabs()
    {
        $active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'sync';

        echo '<h2 class="nav-tab-wrapper">';
        echo '<a href="?page=avsight-sync&tab=sync" class="nav-tab ' . ($active_tab === 'sync' ? 'nav-tab-active' : '') . '">🔄 Sync & Schedule</a>';
        echo '<a href="?page=avsight-sync&tab=delete" class="nav-tab ' . ($active_tab === 'delete' ? 'nav-tab-active' : '') . '">🗑️ Delete Options</a>';
        echo '<a href="?page=avsight-sync&tab=mappings" class="nav-tab ' . ($active_tab === 'mappings' ? 'nav-tab-active' : '') . '">🔄 Data Mappings</a>';
        echo '<a href="?page=avsight-sync&tab=tools" class="nav-tab ' . ($active_tab === 'tools' ? 'nav-tab-active' : '') . '">🛠️ Tools & Settings</a>';
        echo '<a href="?page=avsight-sync&tab=config" class="nav-tab ' . ($active_tab === 'config' ? 'nav-tab-active' : '') . '">⚙️ System & Config</a>';
        echo '</h2>';

        switch ($active_tab) {
            case 'delete':
                self::renderDeleteTab();
                break;
            case 'mappings':
                self::renderMappingsTab();
                break;
            case 'tools':
                self::renderToolsTab();
                break;
            case 'config':
                self::renderConfigTab();
                break;
            default:
                self::renderSyncTab();
                break;
        }
    }

    /**
     * Render sync tab content
     */
    private static function renderSyncTab()
    {
        echo '<div class="avsight-tab-content">';
        echo '<p class="description" style="font-size: 14px; margin-bottom: 20px;">🔄 <strong>Core sync functionality:</strong> Monitor sync status, run manual syncs, and manage the automatic daily schedule.</p>';

        // Current Sync Status
        self::renderSyncStatus();

        // Manual Sync Controls
        self::renderSyncControls();

        // Scheduled Sync
        self::renderScheduledSync();

        echo '</div>';
    }

    /**
     * Render delete tab content
     */
    private static function renderDeleteTab()
    {
        echo '<div class="avsight-tab-content">';
        echo '<p class="description" style="font-size: 14px; margin-bottom: 20px;">🗑️ <strong>Product deletion tools:</strong> Bulk delete WooCommerce products with real-time progress monitoring.</p>';

        // Bulk Deletion Status
        self::renderDeleteStatus();

        // Bulk Delete Controls
        self::renderDeleteControls();

        echo '</div>';
    }

    /**
     * Render tools tab content
     */
    private static function renderToolsTab()
    {
        echo '<div class="avsight-tab-content">';
        echo '<p class="description" style="font-size: 14px; margin-bottom: 20px;">🛠️ <strong>Advanced tools and settings:</strong> Configure sync behavior, test API connections, and manage advanced features.</p>';

        // Performance Settings
        self::renderPerformanceSettings();

        // Product Removal Settings
        self::renderRemovalSettings();

        // Incremental Sync Settings
        self::renderIncrementalSyncSettings();

        // API Diagnostics
        self::renderApiTest();

        // Emergency Recovery Tools
        self::renderEmergencyTools();

        echo '</div>';
    }

    /**
     * Render config tab content
     */
    private static function renderConfigTab()
    {
        echo '<div class="avsight-tab-content">';
        echo '<p class="description" style="font-size: 14px; margin-bottom: 20px;">⚙️ <strong>System configuration:</strong> Check system requirements, verify credentials, and view setup instructions.</p>';

        // Configuration Status
        self::renderConfigStatus();

        // System Requirements
        self::renderSystemRequirements();

        // Configuration Notes
        self::renderConfigNotes();

        echo '</div>';
    }

    /**
     * Render sync status section
     */
    private static function renderSyncStatus()
    {
        $total_items = (int) get_transient(AVSIGHT_SYNC_TOTAL_ITEMS_KEY);
        $processed_items = (int) get_transient(AVSIGHT_SYNC_PROCESSED_ITEMS_KEY);
        $last_run_start = get_transient(AVSIGHT_SYNC_LAST_RUN_START_KEY);
        $last_run_end = get_transient(AVSIGHT_SYNC_LAST_RUN_END_KEY);
        $sync_error = get_transient('avsight_sync_error');

        echo '<div class="avsight-status-box">';
        echo '<h3>📊 Current Sync Status</h3>';

        // Check if sync is actively running
        // Check if sync transient is set OR if we're monitoring a bulk query
        $bulk_query_job_id = get_transient('avsight_bulk_query_job_id');
        $is_running = get_transient(AVSIGHT_SYNC_TRANSIENT_KEY) || $bulk_query_job_id;

        // Only show sync errors if a sync has been attempted (has a start time)
        if ($sync_error && $last_run_start) {
            echo '<div class="notice notice-error"><p><strong>❌ Sync Error:</strong> ' . esc_html($sync_error) . '</p></div>';
        }

        if ($is_running) {
            // Determine status message based on phase
            if ($bulk_query_job_id) {
                echo '<p><strong>Status:</strong> <span id="avsight-sync-status-text" class="status-warning">🔄 Sync In Progress (Waiting for Salesforce)</span></p>';
            } else {
                echo '<p><strong>Status:</strong> <span id="avsight-sync-status-text" class="status-warning">🔄 Sync In Progress</span></p>';
            }

            if ($last_run_start) {
                echo '<p><strong>Started:</strong> ' . date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $last_run_start) . '</p>';
            }

            // Show different display based on whether we're waiting for Salesforce or processing
            if ($total_items == 0 && $bulk_query_job_id) {
                // Waiting for Salesforce - show waiting status
                echo '<p id="avsight-main-status"><strong>Status:</strong> <span style="color: #0073aa;">⏳ Waiting for Salesforce to prepare data...</span></p>';
                echo '<p id="avsight-main-progress"><strong>Progress:</strong> <span style="color: #666;">Preparing bulk query...</span></p>';
                echo '<div class="avsight-progress-bar">';
                echo '<div class="avsight-progress-bar-inner" style="width: 0%; background: linear-gradient(90deg, #0073aa, #00a0d2);">Waiting...</div>';
                echo '</div>';
                echo '<p><strong>Current Phase:</strong> <span id="avsight-sync-current-batch">Requesting data from Salesforce...</span></p>';
            } else {
                // Normal processing display
                echo '<p id="avsight-main-status"><strong>Status:</strong> <span style="color: #0073aa;">🔄 Processing products...</span></p>';
                echo '<p><strong>Total Items:</strong> <span id="avsight-sync-total">' . $total_items . '</span></p>';
                echo '<p><strong>Items Processed:</strong> <span id="avsight-sync-processed">' . $processed_items . '</span></p>';
                $percentage = ($total_items > 0) ? round(($processed_items / $total_items) * 100, 2) : 0;
                echo '<p id="avsight-main-progress"><strong>Progress:</strong> <span id="avsight-sync-percentage">' . $percentage . '%</span></p>';
                echo '<div class="avsight-progress-bar">';
                echo '<div id="avsight-sync-progress-bar-inner" class="avsight-progress-bar-inner" style="width: ' . $percentage . '%;">' . $percentage . '%</div>';
                echo '</div>';
                echo '<p><strong>Time Elapsed:</strong> <span id="avsight-sync-time-elapsed">N/A</span></p>';
                echo '<p><strong>Estimated Time Remaining:</strong> <span id="avsight-sync-time-remaining">N/A</span></p>';
                // Show appropriate batch status based on phase
                if ($bulk_query_job_id) {
                    echo '<p><strong>Current Batch:</strong> <span id="avsight-sync-current-batch">Preparing...</span></p>';
                } else {
                    echo '<p><strong>Current Batch:</strong> <span id="avsight-sync-current-batch">Processing...</span></p>';
                }
            }

            // Add live log section for active sync
            echo '<div class="avsight-log-section" style="margin-top: 15px;">';
            echo '<h4>📋 Live Sync Log</h4>';
            echo '<div id="avsight-sync-log" class="avsight-log-content-inline"></div>';
            echo '<div class="avsight-log-controls-inline">';
            echo '<button type="button" id="avsight-sync-log-clear" class="button button-secondary button-small">Clear Log</button>';
            echo '</div>';
            echo '</div>';
        } else {
            echo '<p><strong>Status:</strong> <span id="avsight-sync-status-text" class="status-good">✅ No sync currently running</span></p>';
            if ($last_run_start && $last_run_end) {
                echo '<p><strong>Last Sync:</strong> ' . date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $last_run_end) . '</p>';
            }

            // Create hidden elements that JavaScript can show/update when sync starts
            echo '<p id="avsight-main-status" style="display: none;"><strong>Status:</strong> <span style="color: #46b450;">✅ Ready to sync</span></p>';
            echo '<p id="avsight-main-progress" style="display: none;"><strong>Progress:</strong> <span style="color: #666;">No sync in progress</span></p>';
            echo '<div class="avsight-progress-bar" style="display: none;">';
            echo '<div class="avsight-progress-bar-inner" style="width: 0%;">0%</div>';
            echo '</div>';

            // Add hidden elements that JavaScript expects to exist
            echo '<p style="display: none;"><strong>Total Items:</strong> <span id="avsight-sync-total">0</span></p>';
            echo '<p style="display: none;"><strong>Items Processed:</strong> <span id="avsight-sync-processed">0</span></p>';
            echo '<p style="display: none;"><strong>Time Elapsed:</strong> <span id="avsight-sync-time-elapsed">N/A</span></p>';
            echo '<p style="display: none;"><strong>Estimated Time Remaining:</strong> <span id="avsight-sync-time-remaining">N/A</span></p>';
            echo '<p id="avsight-sync-current-batch" style="display: none;"><strong>Current Phase:</strong> <span>Idle</span></p>';
        }

        echo '</div>';
    }

    /**
     * Render sync controls section
     */
    private static function renderSyncControls()
    {
        echo '<div class="avsight-status-box">';
        echo '<h3>🚀 Manual Sync Controls</h3>';
        echo '<p><strong>Manual Override:</strong> Start a full inventory synchronization immediately, independent of the scheduled sync.</p>';
        echo '<p><em>Note: This will clear any existing sync progress and start fresh.</em></p>';
        echo '<form method="post" action="">';
        wp_nonce_field('avsight_manual_sync', 'avsight_sync_nonce');
        echo '<p><input type="submit" name="submit" id="submit" class="button button-primary" value="🔄 Start Manual Sync" title="Start a fresh sync operation (clears existing progress)"></p>';
        echo '<button type="button" id="avsight-stop-sync" class="button button-secondary" style="display: none;">⏹️ Stop Sync</button>';
        echo '</form>';
        echo '</div>';
    }

    /**
     * Render performance settings section
     */
    private static function renderPerformanceSettings()
    {
        echo '<div class="avsight-status-box">';
        echo '<h3>⚡ Performance Settings</h3>';
        echo '<p><strong>Batch Processing:</strong> Configure batch sizes and sync limits for optimal performance.</p>';
        echo '<p style="color: #666; font-size: 13px;">🔧 <em>Adjust these settings to optimize performance for your server</em></p>';

        // Get current settings
        $sync_batch_size = get_option('avsight_sync_batch_size', AVSIGHT_SYNC_BATCH_SIZE);
        $delete_batch_size = get_option('avsight_delete_batch_size', AVSIGHT_BULK_DELETE_BATCH_SIZE);
        $sync_limit = get_option('avsight_sync_limit', 0); // 0 = no limit
        $debug_mode = get_option('avsight_debug_mode', false);

        echo '<form method="post" action="">';
        wp_nonce_field('avsight_performance_settings', 'avsight_performance_nonce');
        echo '<input type="hidden" name="avsight_action" value="update_performance_settings">';

        echo '<table class="form-table">';

        // Sync Batch Size
        echo '<tr>';
        echo '<th scope="row">Sync Batch Size</th>';
        echo '<td>';
        echo '<input type="number" name="sync_batch_size" value="' . esc_attr($sync_batch_size) . '" min="1" max="200" class="small-text">';
        echo '<p class="description">Number of products to process per batch during sync. Lower values use less memory but take longer. Default: 50</p>';
        echo '</td>';
        echo '</tr>';

        // Delete Batch Size
        echo '<tr>';
        echo '<th scope="row">Delete Batch Size</th>';
        echo '<td>';
        echo '<input type="number" name="delete_batch_size" value="' . esc_attr($delete_batch_size) . '" min="1" max="200" class="small-text">';
        echo '<p class="description">Number of products to delete per batch. Lower values are safer but slower. Default: 50</p>';
        echo '</td>';
        echo '</tr>';

        // Debug Mode
        echo '<tr>';
        echo '<th scope="row">Debug Mode</th>';
        echo '<td>';
        echo '<label><input type="checkbox" name="debug_mode" value="1"' . checked($debug_mode, true, false) . '> <strong>Enable debug mode</strong></label><br>';
        echo '<small>Enables sync limit and additional logging for testing purposes.</small>';
        echo '</td>';
        echo '</tr>';

        // Safe Mode for Limited Syncs
        $safe_mode = get_option('avsight_safe_mode_limited_sync', true);
        echo '<tr>';
        echo '<th scope="row">Limited Sync Safety</th>';
        echo '<td>';
        echo '<label><input type="checkbox" name="safe_mode_limited_sync" value="1"' . checked($safe_mode, true, false) . '> <strong>Protect products during limited syncs</strong></label><br>';
        echo '<small>When enabled, prevents product removal during debug syncs with limits to avoid accidentally removing products not in the limited batch.</small>';
        echo '</td>';
        echo '</tr>';

        // Sync Limit (only show if debug mode is enabled)
        echo '<tr id="sync-limit-row" style="' . ($debug_mode ? '' : 'display: none;') . '">';
        echo '<th scope="row">Sync Limit <span style="color: #d63638;">(Debug Only)</span></th>';
        echo '<td>';
        echo '<input type="number" name="sync_limit" value="' . esc_attr($sync_limit) . '" min="0" max="10000" class="small-text">';
        echo '<p class="description"><strong>⚠️ Debug only:</strong> Limit number of products to sync (0 = no limit). Only use for testing!</p>';
        echo '</td>';
        echo '</tr>';

        echo '</table>';

        echo '<p>';
        echo '<input type="submit" name="submit" class="button button-primary" value="💾 Save Performance Settings">';
        echo '</p>';
        echo '</form>';

        // Current values display
        echo '<hr>';
        echo '<h4>📊 Current Settings</h4>';
        echo '<ul>';
        echo '<li><strong>Sync Batch Size:</strong> ' . $sync_batch_size . ' products per batch</li>';
        echo '<li><strong>Delete Batch Size:</strong> ' . $delete_batch_size . ' products per batch</li>';
        if ($debug_mode) {
            echo '<li><strong>Debug Mode:</strong> <span style="color: orange;">Enabled</span></li>';
            if ($sync_limit > 0) {
                echo '<li><strong>Sync Limit:</strong> <span style="color: red;">' . $sync_limit . ' products (DEBUG ONLY)</span></li>';
            } else {
                echo '<li><strong>Sync Limit:</strong> No limit</li>';
            }
        } else {
            echo '<li><strong>Debug Mode:</strong> <span style="color: green;">Disabled (Production)</span></li>';
            echo '<li><strong>Sync Limit:</strong> No limit (Production mode)</li>';
        }
        echo '</ul>';

        echo '</div>';
    }

    /**
     * Render product removal settings section
     */
    private static function renderRemovalSettings()
    {
        echo '<div class="avsight-status-box">';
        echo '<h3>🗑️ Product Removal Settings</h3>';
        echo '<p><strong>Automatic Cleanup:</strong> Configure how to handle products that are no longer in Avsight inventory.</p>';
        echo '<p style="color: #666; font-size: 13px;">⚙️ <em>Settings for managing products that disappear from Avsight inventory</em></p>';

        $removal_action = get_option('avsight_removal_action', 'out_of_stock');

        echo '<form method="post" action="">';
        wp_nonce_field('avsight_removal_settings', 'avsight_removal_nonce');
        echo '<input type="hidden" name="avsight_action" value="update_removal_settings">';

        echo '<table class="form-table">';
        echo '<tr>';
        echo '<th scope="row">Removal Action</th>';
        echo '<td>';
        echo '<label><input type="radio" name="removal_action" value="out_of_stock"' . checked($removal_action, 'out_of_stock', false) . '> <strong>Set out of stock</strong> (Recommended)</label><br>';
        echo '<small>Products become unavailable for purchase but remain in your catalog for reference.</small><br><br>';
        echo '<label><input type="radio" name="removal_action" value="disable"' . checked($removal_action, 'disable', false) . '> <strong>Disable products</strong> (Draft status)</label><br>';
        echo '<small>Products are hidden from customers but remain in admin for manual review.</small><br><br>';
        echo '<label><input type="radio" name="removal_action" value="delete"' . checked($removal_action, 'delete', false) . '> <strong>Delete products</strong> (Permanent)</label><br>';
        echo '<small style="color: #d63638;">⚠️ Permanently removes products and all associated data. Cannot be undone.</small>';
        echo '<p class="description">This action applies to products that are no longer found in Avsight inventory during sync.</p>';
        echo '</td>';
        echo '</tr>';
        echo '</table>';

        echo '<p>';
        echo '<input type="submit" name="submit" class="button" value="💾 Save Removal Settings">';
        echo '</p>';
        echo '</form>';
        echo '</div>';
    }

    /**
     * Render scheduled sync section
     */
    private static function renderScheduledSync()
    {
        $schedule_status = AvsightScheduler::getScheduleStatus();
        $cron_info = AvsightScheduler::isCronWorking();

        echo '<div class="avsight-status-box">';
        echo '<h3>⏰ Scheduled Sync</h3>';
        echo '<p><strong>Automatic Sync:</strong> Runs every night at 2:00 AM to keep your inventory synchronized with Avsight.</p>';

        // Cron status
        if (!$cron_info['working']) {
            echo '<div class="notice notice-error"><p><strong>⚠️ Cron Issue:</strong> ' . esc_html($cron_info['message']) . '</p></div>';
        }

        // Daily sync status
        if ($schedule_status['daily_sync']['scheduled']) {
            echo '<p><strong>Status:</strong> <span class="status-good">✅ Scheduled</span></p>';
            echo '<p><strong>Next Run:</strong> ' . esc_html($schedule_status['daily_sync']['next_run_formatted']) . '</p>';
            echo '<p><strong>Time Until Next Run:</strong> ' . esc_html($schedule_status['daily_sync']['time_until']) . '</p>';
            echo '<p><strong>Schedule:</strong> Daily at 2:00 AM (server time)</p>';
        } else {
            echo '<p><strong>Status:</strong> <span class="status-error">❌ Not Scheduled</span></p>';
            echo '<p>The automatic daily sync is not currently scheduled.</p>';
        }

        // Batch processing status
        if ($schedule_status['batch_processing']['active']) {
            echo '<p><strong>Active Batch:</strong> <span class="status-warning">🔄 Processing</span></p>';
            echo '<p><strong>Next Batch:</strong> ' . esc_html($schedule_status['batch_processing']['next_batch_formatted']) . '</p>';
        } else {
            echo '<p><strong>Active Batch:</strong> <span class="status-good">✅ No active batch</span></p>';
        }

        // Control buttons
        echo '<div style="margin-top: 15px;">';

        if ($schedule_status['daily_sync']['scheduled']) {
            // Reschedule button with tooltip
            echo '<form method="post" action="" style="display: inline-block; margin-right: 10px;" title="Reset the schedule to the next 2 AM (useful if schedule got corrupted)">';
            wp_nonce_field('avsight_reschedule_sync', 'avsight_reschedule_nonce');
            echo '<input type="submit" name="submit" class="button button-secondary" value="🔄 Reset Schedule">';
            echo '</form>';

            // Unschedule button with clear warning
            echo '<form method="post" action="" style="display: inline-block; margin-right: 10px;" onsubmit="return confirm(\'⚠️ This will COMPLETELY DISABLE automatic daily sync.\\n\\nNo more automatic syncs will run until you re-enable the schedule.\\n\\nAre you sure?\');">';
            wp_nonce_field('avsight_unschedule_sync', 'avsight_unschedule_nonce');
            echo '<input type="submit" name="submit" class="button button-secondary" value="❌ Disable Auto-Sync">';
            echo '</form>';
        } else {
            // Schedule button
            echo '<form method="post" action="" style="display: inline-block; margin-right: 10px;">';
            wp_nonce_field('avsight_schedule_sync', 'avsight_schedule_nonce');
            echo '<input type="submit" name="submit" class="button button-primary" value="⏰ Enable Auto-Sync">';
            echo '</form>';
        }

        // Force run button with clear description
        echo '<form method="post" action="" style="display: inline-block;" title="Run the scheduled sync immediately (doesn\'t affect the schedule)">';
        wp_nonce_field('avsight_force_daily_sync', 'avsight_force_sync_nonce');
        echo '<input type="submit" name="submit" class="button button-secondary" value="▶️ Run Scheduled Sync Now">';
        echo '</form>';

        echo '</div>';
        echo '</div>';
    }

    /**
     * Render incremental sync settings section
     */
    private static function renderIncrementalSyncSettings()
    {
        echo '<div class="avsight-status-box">';
        echo '<h3>⚡ Incremental Sync Settings</h3>';
        echo '<p><strong>Performance Optimization:</strong> Only sync products that have changed since the last successful sync.</p>';
        echo '<p style="color: #666; font-size: 13px;">🚀 <em>Dramatically improves sync speed for large inventories</em></p>';

        // Get current settings AFTER any form processing
        $last_sync = get_option('avsight_last_successful_sync', false);

        // Check if we just updated via redirect, use that value instead of cached option
        if (isset($_GET['incremental_updated'])) {
            $incremental_enabled = $_GET['incremental_updated'] === '1';
        } else {
            $incremental_enabled = get_option('avsight_incremental_sync_enabled', false);
        }

        if ($last_sync) {
            $last_sync_formatted = date('Y-m-d H:i:s', strtotime($last_sync));
            echo '<p><strong>Last Successful Sync:</strong> <span style="color: green;">' . esc_html($last_sync_formatted) . '</span></p>';
            echo '<p><strong>Next Sync Will Be:</strong> <span style="color: blue;">Incremental (only changed products)</span></p>';
        } else {
            echo '<p><strong>Last Successful Sync:</strong> <span style="color: orange;">None found</span></p>';
            echo '<p><strong>Next Sync Will Be:</strong> <span style="color: blue;">Full sync (all products)</span></p>';
        }

        echo '<form method="post" action="">';
        wp_nonce_field('avsight_incremental_settings', 'avsight_incremental_nonce');
        echo '<input type="hidden" name="avsight_action" value="update_incremental_settings">';

        echo '<table class="form-table">';
        echo '<tr>';
        echo '<th scope="row">Incremental Sync</th>';
        echo '<td>';
        echo '<label><input type="checkbox" name="incremental_enabled" value="1"' . checked($incremental_enabled, true, false) . '> <strong>Enable incremental sync</strong></label><br>';
        echo '<small>When enabled, only products modified since the last sync will be processed, significantly improving performance for large inventories.</small>';
        echo '<p class="description">Disable this to force full syncs every time (useful for troubleshooting).</p>';
        echo '</td>';
        echo '</tr>';
        echo '</table>';

        echo '<p>';
        echo '<input type="submit" name="submit" class="button" value="💾 Save Incremental Settings">';
        echo '</p>';
        echo '</form>';

        // Reset timestamp controls - only show if incremental sync is enabled
        if ($last_sync && $incremental_enabled) {
            echo '<hr>';
            echo '<h4>🔄 Reset Sync History</h4>';
            echo '<form method="post" action="" style="display: inline;">';
            wp_nonce_field('avsight_reset_sync_timestamp', 'avsight_reset_timestamp_nonce');
            echo '<input type="hidden" name="avsight_action" value="reset_sync_timestamp">';
            echo '<p>Force the next sync to be a full sync by clearing the last sync timestamp.</p>';
            echo '<input type="submit" name="submit" class="button" value="🔄 Reset Sync History (Force Full Sync)" onclick="return confirm(\'This will force the next sync to process all products. Continue?\');">';
            echo '</form>';
        }

        echo '</div>';
    }

    /**
     * Render API test section
     */
    private static function renderApiTest()
    {
        echo '<div class="avsight-status-box">';
        echo '<h3>🧪 API Diagnostics</h3>';
        echo '<p style="color: #666; font-size: 13px;">🔍 <em>Test and troubleshoot Salesforce API connections</em></p>';

        // Basic API connection test
        echo '<h4>🔗 Basic Connection Test</h4>';
        echo '<form method="post" action="" style="display: inline-block; margin-right: 10px;">';
        wp_nonce_field('avsight_test_api_connection', 'avsight_test_api_nonce');
        echo '<p>Test authentication and token retrieval.</p>';
        echo '<p><input type="submit" name="submit" class="button button-secondary" value="🧪 Test Authentication"></p>';
        echo '</form>';

        // Bulk API access test
        echo '<h4>📊 Bulk API Access Test</h4>';
        echo '<form method="post" action="" style="display: inline-block; margin-right: 10px;">';
        wp_nonce_field('avsight_test_api_access', 'avsight_test_api_access_nonce');
        echo '<p>Test Bulk API access with standard objects (Account).</p>';
        echo '<p><input type="submit" name="submit" class="button button-secondary" value="🔍 Test Bulk API"></p>';
        echo '</form>';

        // List available objects
        echo '<h4>📋 Available Objects</h4>';
        echo '<form method="post" action="" style="display: inline-block; margin-right: 10px;">';
        wp_nonce_field('avsight_list_objects', 'avsight_list_objects_nonce');
        echo '<p>List all available inventory-related objects in your Salesforce environment.</p>';
        echo '<p><input type="submit" name="submit" class="button button-secondary" value="📋 List Objects"></p>';
        echo '</form>';

        // Describe inventory object
        echo '<h4>🔍 Inventory Object Fields</h4>';
        echo '<form method="post" action="" style="display: inline-block; margin-right: 10px;">';
        wp_nonce_field('avsight_describe_object', 'avsight_describe_object_nonce');
        echo '<p>Get detailed field information for the inscor__Inventory_Line__c object.</p>';
        echo '<p><input type="submit" name="submit" class="button button-secondary" value="🔍 Describe Inventory Object"></p>';
        echo '</form>';

        // Discover aircraft fields
        echo '<h4>🚁 Discover Aircraft Fields</h4>';
        echo '<form method="post" action="" style="display: inline-block; margin-right: 10px;">';
        wp_nonce_field('avsight_discover_aircraft_fields', 'avsight_discover_aircraft_nonce');
        echo '<p>Search for aircraft/helicopter related fields in Salesforce for WooCommerce attributes.</p>';
        echo '<p><input type="submit" name="submit" class="button button-secondary" value="🚁 Find Aircraft Fields"></p>';
        echo '</form>';

        // Sample aircraft field values
        echo '<h4>📊 Sample Aircraft Field Values</h4>';
        echo '<form method="post" action="" style="display: inline-block; margin-right: 10px;">';
        wp_nonce_field('avsight_sample_aircraft_values', 'avsight_sample_aircraft_nonce');
        echo '<p>Get sample values from aircraft-related fields to see what data they contain.</p>';
        echo '<p><input type="submit" name="submit" class="button button-secondary" value="📊 Sample Field Values"></p>';
        echo '</form>';

        // Debug current sync fields
        echo '<h4>🔍 Debug Current Sync Fields</h4>';
        echo '<form method="post" action="" style="display: inline-block; margin-right: 10px;">';
        wp_nonce_field('avsight_debug_sync_fields', 'avsight_debug_sync_fields_nonce');
        echo '<p><strong>🎯 Perfect for finding aircraft model fields!</strong> Shows exactly what fields and data are returned by the current sync query.</p>';
        echo '<p>This tool runs the same query as the sync but limits to 3 records to show you all available fields and sample data.</p>';
        echo '<p><input type="submit" name="submit" class="button button-primary" value="🔍 Debug Sync Fields"></p>';
        echo '</form>';

        // Debug product fields
        echo '<h4>🚁 Debug Product Object Fields</h4>';
        echo '<form method="post" action="" style="display: inline-block; margin-right: 10px;">';
        wp_nonce_field('avsight_debug_product_fields', 'avsight_debug_product_fields_nonce');
        echo '<p><strong>🎯 Check for aircraft model data on Product2 object!</strong> Explores the Product object to find aircraft/helicopter model fields.</p>';
        echo '<p>This tool queries the Product2 object directly to see if aircraft model data is stored there instead of on the inventory records.</p>';
        echo '<p><input type="submit" name="submit" class="button button-secondary" value="🚁 Debug Product Fields"></p>';
        echo '</form>';

        // Describe Product2 object
        echo '<h4>📋 Describe Product2 Object</h4>';
        echo '<form method="post" action="" style="display: inline-block; margin-right: 10px;">';
        wp_nonce_field('avsight_describe_product2', 'avsight_describe_product2_nonce');
        echo '<p><strong>🔍 See ALL Product2 fields!</strong> Uses Salesforce describe API to show every available field on the Product2 object.</p>';
        echo '<p>This will show you exactly what aircraft-related fields exist and their types. Perfect for finding the right field names!</p>';
        echo '<p><input type="submit" name="submit" class="button button-primary" value="📋 Describe Product2"></p>';
        echo '</form>';

        // Test aircraft data with new fields
        echo '<h4>🚁 Test Aircraft Data</h4>';
        echo '<form method="post" action="" style="display: inline-block; margin-right: 10px;">';
        wp_nonce_field('avsight_test_aircraft_data', 'avsight_test_aircraft_data_nonce');
        echo '<p><strong>🎯 Test the new aircraft fields!</strong> Shows what aircraft model data we get from the updated sync query with Applicability fields.</p>';
        echo '<p>This tests the actual aircraft data from inscor__Applicability__c and inscor__Applicability_Detailed__c fields we found.</p>';
        echo '<p><input type="submit" name="submit" class="button button-primary" value="🚁 Test Aircraft Data"></p>';
        echo '</form>';

        // Debug WooCommerce aircraft data
        echo '<h4>🔍 Debug WooCommerce Aircraft Data</h4>';
        echo '<form method="post" action="" style="display: inline-block; margin-right: 10px;">';
        wp_nonce_field('avsight_debug_wc_aircraft', 'avsight_debug_wc_aircraft_nonce');
        echo '<p><strong>🎯 See what aircraft data is actually in WooCommerce!</strong> Shows exactly what aircraft attributes and values are stored in your products.</p>';
        echo '<p>This will help us understand the data mismatch between Salesforce and WooCommerce.</p>';
        echo '<p><input type="submit" name="submit" class="button button-primary" value="🔍 Debug WooCommerce Aircraft"></p>';
        echo '</form>';

        // Debug note about data mismatch
        echo '<div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 10px; margin: 15px 0;">';
        echo '<h4 style="margin-top: 0;">🔍 Data Mismatch Investigation</h4>';
        echo '<p><strong>Issue:</strong> Debug tools show "207s/206s" and "B/D/PW/37A" but WooCommerce shows "145", "OEM", "Repair", "Station"</p>';
        echo '<p><strong>Possible Causes:</strong></p>';
        echo '<ul>';
        echo '<li>🔄 <strong>Bulk API vs REST API:</strong> Different data returned by bulk query vs direct query</li>';
        echo '<li>📊 <strong>Different Records:</strong> Bulk query might return different products than the test query</li>';
        echo '<li>🔀 <strong>Data Processing:</strong> Aircraft data might be getting processed/parsed differently</li>';
        echo '</ul>';
        echo '<p><strong>Next Steps:</strong> Use the "🔍 Debug WooCommerce Aircraft" tool to see what\'s actually stored in WooCommerce.</p>';
        echo '</div>';

        // Clear cache button
        echo '<h4>🧹 Clear Error Cache</h4>';
        echo '<form method="post" action="">';
        wp_nonce_field('avsight_clear_cache', 'avsight_clear_cache_nonce');
        echo '<p>Clear all cached errors and sync status. Use this if you see old error messages.</p>';
        echo '<p><input type="submit" name="submit" class="button button-secondary" value="🧹 Clear Cache"></p>';
        echo '</form>';

        echo '</div>';
    }

    /**
     * Render emergency recovery tools section
     */
    private static function renderEmergencyTools()
    {
        echo '<div class="avsight-status-box">';
        echo '<h3>🚨 Emergency Recovery Tools</h3>';
        echo '<p style="color: #666; font-size: 13px;">⚠️ <em>Use these tools only when needed to recover from sync issues</em></p>';

        echo '<h4>🛑 Emergency Sync Stop</h4>';
        echo '<p><strong>Issue:</strong> Sync is stuck or not responding to the normal stop button</p>';
        echo '<p><strong>Solution:</strong> This will immediately clear all sync processes, transients, and reset the display.</p>';

        echo '<form method="post" action="" style="display: inline-block; margin-right: 10px;">';
        wp_nonce_field('avsight_emergency_stop_sync', 'avsight_emergency_stop_nonce');
        echo '<input type="hidden" name="avsight_action" value="emergency_stop_sync">';
        echo '<p><input type="submit" name="submit" class="button button-secondary" value="🛑 Emergency Stop Sync" onclick="return confirm(\'This will immediately stop all sync operations and clear all progress. Continue?\')"></p>';
        echo '</form>';

        echo '<h4>🔄 Product Stock Recovery</h4>';
        echo '<p><strong>Issue:</strong> If an incremental sync incorrectly set all products to "out of stock"</p>';
        echo '<p><strong>Solution:</strong> This tool will restore all Avsight-synced products back to "in stock" status with default quantities.</p>';

        echo '<form method="post" action="" style="display: inline-block; margin-right: 10px;">';
        wp_nonce_field('avsight_emergency_restore', 'avsight_emergency_restore_nonce');
        echo '<p><input type="submit" name="submit" class="button button-secondary" value="🔄 Restore Product Stock" onclick="return confirm(\'Are you sure you want to restore all out-of-stock Avsight products? This will set them back to in-stock with default quantities.\')"></p>';
        echo '</form>';

        echo '<h4>🔄 Force Full Sync</h4>';
        echo '<p><strong>Issue:</strong> System keeps doing incremental sync instead of full sync</p>';
        echo '<p><strong>Solution:</strong> This will clear the sync history and force the next sync to be a full sync.</p>';

        echo '<form method="post" action="" style="display: inline-block; margin-right: 10px;">';
        wp_nonce_field('avsight_force_full_sync', 'avsight_force_full_sync_nonce');
        echo '<p><input type="submit" name="submit" class="button button-secondary" value="🔄 Force Full Sync" onclick="return confirm(\'This will clear sync history and force a full sync. Continue?\')"></p>';
        echo '</form>';

        echo '<div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 10px; margin-top: 10px;">';
        echo '<p style="margin: 0; color: #856404;"><strong>⚠️ Warning:</strong> Only use this tool if you know products were incorrectly set to out-of-stock by a sync bug. This will override actual inventory levels from Avsight.</p>';
        echo '</div>';

        echo '</div>';
    }

    /**
     * Render delete status section
     */
    private static function renderDeleteStatus()
    {
        $delete_status = AvsightDeleteOperations::getDeleteStatus();

        echo '<div class="avsight-status-box">';
        echo '<h3>🗑️ Bulk Deletion Status</h3>';

        $delete_error = get_transient('avsight_delete_error');
        if ($delete_error) {
            echo '<div class="notice notice-error"><p><strong>❌ Delete Error:</strong> ' . esc_html($delete_error) . '</p></div>';
        }

        if ($delete_status['running']) {
            echo '<p><strong>Status:</strong> <span id="avsight-delete-status-text" class="status-warning">🗑️ Deletion In Progress</span></p>';
            echo '<p><strong>Total Products to Delete:</strong> <span id="avsight-delete-total">' . $delete_status['total'] . '</span></p>';
            echo '<p><strong>Products Deleted:</strong> <span id="avsight-delete-processed">' . $delete_status['processed'] . '</span></p>';
            echo '<p><strong>Progress:</strong> <span id="avsight-delete-percentage">' . $delete_status['percentage'] . '%</span></p>';
            echo '<div class="avsight-progress-bar">';
            echo '<div id="avsight-delete-progress-bar-inner" class="avsight-progress-bar-inner" style="width: ' . $delete_status['percentage'] . '%; background-color: #dc3232;">' . $delete_status['percentage'] . '%</div>';
            echo '</div>';
            echo '<p><strong>Time Elapsed:</strong> <span id="avsight-delete-time-elapsed">N/A</span></p>';
            echo '<p><strong>Estimated Time Remaining:</strong> <span id="avsight-delete-time-remaining">N/A</span></p>';
            echo '<p><strong>Current Batch:</strong> <span id="avsight-delete-current-batch">Processing...</span></p>';

            // Add live log section for active deletion
            echo '<div class="avsight-log-section" style="margin-top: 15px;">';
            echo '<h4>📋 Live Deletion Log</h4>';
            echo '<div id="avsight-delete-log" class="avsight-log-content-inline"></div>';
            echo '<div class="avsight-log-controls-inline">';
            echo '<button type="button" id="avsight-delete-log-clear" class="button button-secondary button-small">Clear Log</button>';
            echo '</div>';
            echo '</div>';
        } else {
            echo '<p class="status-good">✅ No deletion currently running</p>';
            if ($delete_status['total'] > 0 && $delete_status['processed'] >= $delete_status['total']) {
                echo '<p><strong>Last Deletion:</strong> Completed - ' . $delete_status['processed'] . ' products deleted</p>';
            }
        }

        echo '</div>';
    }

    /**
     * Render delete controls section
     */
    private static function renderDeleteControls()
    {
        echo '<div class="avsight-status-box">';
        echo '<h3>🗑️ Bulk Product Deletion</h3>';
        echo '<div class="notice notice-warning"><p><strong>⚠️ Warning:</strong> This action will permanently delete ALL WooCommerce products. This cannot be undone!</p></div>';
        echo '<form method="post" action="" onsubmit="return confirm(\'Are you absolutely sure you want to delete ALL products? This action cannot be undone!\');">';
        wp_nonce_field('avsight_bulk_delete_products', 'avsight_bulk_delete_nonce');
        echo '<p><strong>Bulk Product Deletion:</strong> Remove all products from WooCommerce. Use this before a fresh sync.</p>';
        echo '<p><input type="submit" name="submit" id="submit" class="button button-danger" value="🗑️ Delete All Products" style="background-color: #dc3232; border-color: #dc3232; color: white;"></p>';
        echo '<button type="button" id="avsight-stop-delete" class="button button-secondary" style="display: none;">⏹️ Stop Deletion</button>';
        echo '</form>';
        echo '</div>';
    }

    /**
     * Render configuration status section
     */
    private static function renderConfigStatus()
    {
        $config_status = AvsightApi::getDetailedConfigStatus();
        $all_configured = true;
        $required_count = 0;
        $configured_count = 0;

        // Count required vs configured (only client credentials needed now)
        foreach ($config_status as $constant => $status) {
            if (in_array($constant, ['AVSIGHT_SF_CONSUMER_KEY', 'AVSIGHT_SF_CONSUMER_SECRET'])) {
                $required_count++;
                if ($status['configured']) {
                    $configured_count++;
                } else {
                    $all_configured = false;
                }
            }
        }

        echo '<div class="avsight-status-box">';
        echo '<h3>🔧 Configuration Status</h3>';

        // Overall status
        if ($all_configured) {
            echo '<div class="notice notice-success"><p><strong>✅ Configuration Complete:</strong> All ' . $required_count . ' required credentials are configured.</p></div>';
        } else {
            echo '<div class="notice notice-error"><p><strong>❌ Configuration Incomplete:</strong> ' . $configured_count . ' of ' . $required_count . ' required credentials are configured.</p></div>';
        }

        // Detailed table
        echo '<table class="widefat" style="margin-top: 15px;">';
        echo '<thead><tr><th>Credential</th><th>Description</th><th>Status</th><th>Value</th></tr></thead>';
        echo '<tbody>';
        foreach ($config_status as $constant => $status) {
            $status_class = 'status-' . $status['status'];

            // Determine icon and text based on status
            if ($status['configured']) {
                $status_icon = '✅';
                $status_text = 'Configured';
            } elseif ($status['status'] === 'optional') {
                $status_icon = '➖';
                $status_text = 'Optional';
                $status_class = 'status-optional';
            } else {
                $status_icon = '❌';
                $status_text = 'Missing';
            }

            echo '<tr>';
            echo '<td><strong>' . esc_html($status['name']) . '</strong><br><code>' . esc_html($constant) . '</code></td>';
            echo '<td>' . esc_html($status['description']) . '</td>';
            echo '<td class="' . $status_class . '">' . $status_icon . ' ' . esc_html($status_text) . '</td>';
            echo '<td><code>' . esc_html($status['display_value']) . '</code></td>';
            echo '</tr>';
        }
        echo '</tbody></table>';

        // Environment detection
        $environment = 'Unknown';
        $env_color = 'orange';
        if (defined('AVSIGHT_SF_LOGIN_URL')) {
            if (strpos(AVSIGHT_SF_LOGIN_URL, 'sandbox.my.salesforce.com') !== false) {
                $environment = 'Sandbox';
                $env_color = 'blue';
            } elseif (strpos(AVSIGHT_SF_LOGIN_URL, 'login.salesforce.com') !== false) {
                $environment = 'Production';
                $env_color = 'green';
            }
        }

        echo '<div style="margin-top: 15px; padding: 10px; background: #f9f9f9; border-left: 4px solid ' . $env_color . ';">';
        echo '<strong>🌍 Environment:</strong> <span style="color: ' . $env_color . '; font-weight: bold;">' . $environment . '</span>';
        if (defined('AVSIGHT_SF_LOGIN_URL')) {
            echo '<br><strong>Login URL:</strong> <code>' . esc_html(AVSIGHT_SF_LOGIN_URL) . '</code>';
        }
        echo '</div>';

        echo '</div>';
    }

    /**
     * Render system requirements section
     */
    private static function renderSystemRequirements()
    {
        $requirements = AvsightApi::checkSystemRequirements();

        echo '<div class="avsight-status-box">';
        echo '<h3>⚙️ System Requirements</h3>';
        echo '<table class="widefat">';
        echo '<thead><tr><th>Component</th><th>Current</th><th>Required</th><th>Status</th><th>Message</th></tr></thead>';
        echo '<tbody>';

        foreach ($requirements as $req) {
            $status_class = 'status-' . $req['status'];
            $status_icon = $req['status'] === 'good' ? '✅' : '❌';

            echo '<tr>';
            echo '<td><strong>' . esc_html($req['name']) . '</strong></td>';
            echo '<td>' . esc_html($req['current']) . '</td>';
            echo '<td>' . esc_html($req['required']) . '</td>';
            echo '<td class="' . $status_class . '">' . $status_icon . '</td>';
            echo '<td>' . esc_html($req['message']) . '</td>';
            echo '</tr>';
        }

        echo '</tbody></table>';
        echo '</div>';
    }

    /**
     * Render configuration notes section
     */
    private static function renderConfigNotes()
    {
        echo '<div class="avsight-status-box">';
        echo '<h3>📝 Configuration Notes</h3>';

        echo '<h4>🔑 Required Credentials (Client Credentials Flow)</h4>';
        echo '<p>Configure these <strong>required</strong> environment variables in your .env file:</p>';
        echo '<ul>';
        echo '<li><code>AVSIGHT_SF_CONSUMER_KEY</code> - Connected App Consumer Key</li>';
        echo '<li><code>AVSIGHT_SF_CONSUMER_SECRET</code> - Connected App Consumer Secret</li>';
        echo '<li><code>AVSIGHT_SF_API_VERSION</code> - API version (e.g., v55.0)</li>';
        echo '</ul>';

        echo '<h4>🚫 No Longer Required</h4>';
        echo '<p>These credentials are <strong>not needed</strong> with the client credentials flow:</p>';
        echo '<ul>';
        echo '<li><code>AVSIGHT_SF_USERNAME</code> - Not needed</li>';
        echo '<li><code>AVSIGHT_SF_PASSWORD</code> - Not needed</li>';
        echo '<li><code>AVSIGHT_SF_SECURITY_TOKEN</code> - Not needed</li>';
        echo '</ul>';

        echo '<h4>🔧 Connected App Setup</h4>';
        echo '<p>Ensure your Salesforce Connected App is configured for:</p>';
        echo '<ul>';
        echo '<li>✅ <strong>Client Credentials Flow</strong> enabled</li>';
        echo '<li>✅ <strong>API access</strong> permissions</li>';
        echo '<li>✅ <strong>Bulk API</strong> permissions</li>';
        echo '</ul>';

        echo '<p>Also, verify the product mapping logic in the <code>updateWooCommerceProduct</code> function if your WooCommerce product SKUs/titles do not directly match Avsight\'s <code>inscor__Product__r.Name</code>.</p>';

        echo '<h4>🔐 API Token Usage</h4>';
        echo '<p><strong>Important:</strong> Salesforce access tokens are only requested when:</p>';
        echo '<ul>';
        echo '<li>✅ You manually click "Test API Connection"</li>';
        echo '<li>✅ You start a sync operation</li>';
        echo '<li>❌ <strong>NOT</strong> when simply viewing this page</li>';
        echo '</ul>';
        echo '<p>Tokens are short-lived (typically 2 hours) and Salesforce has generous API limits. Each sync operation uses only 1-3 API calls regardless of inventory size thanks to the Bulk API.</p>';
        echo '</div>';
    }

    /**
     * Enqueue admin scripts and styles
     */
    public static function enqueueAdminAssets()
    {
        if (isset($_GET['page']) && $_GET['page'] === 'avsight-sync') {
            wp_enqueue_script('jquery');
            self::renderAdminScript();
        }
    }

    /**
     * Render admin JavaScript
     */
    private static function renderAdminScript()
    {
?>
        <style>
            .avsight-log-content-inline {
                background: #f9f9f9;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 10px;
                height: 200px;
                overflow-y: auto;
                font-family: monospace;
                font-size: 12px;
                line-height: 1.4;
                margin-bottom: 10px;
            }

            .avsight-log-entry {
                margin-bottom: 2px;
                padding: 2px 0;
            }

            .avsight-log-timestamp {
                color: #666;
                font-weight: bold;
            }

            .avsight-log-level-info {
                color: #0073aa;
            }

            .avsight-log-level-success {
                color: #46b450;
            }

            .avsight-log-level-warning {
                color: #ffb900;
            }

            .avsight-log-level-error {
                color: #dc3232;
            }

            .avsight-log-controls-inline {
                text-align: right;
            }

            .avsight-log-section {
                background: #fff;
                border: 1px solid #c3c4c7;
                border-radius: 4px;
                padding: 15px;
                margin-top: 15px;
            }
        </style>
        <script type="text/javascript">
            jQuery(document).ready(function($) {
                var ajaxurl = '<?php echo admin_url('admin-ajax.php'); ?>';
                var nonce = '<?php echo wp_create_nonce('avsight_sync_status_nonce'); ?>';
                var isSyncRunning = false;

                // Check if sync is already running on page load
                var initialSyncStatus = $('#avsight-sync-status-text').text();
                if (initialSyncStatus.includes('Sync In Progress')) {
                    isSyncRunning = true;
                }

                // Add sync log entry
                function addSyncLogEntry(level, message) {
                    var timestamp = new Date().toLocaleTimeString();
                    var logEntry = '<div class="avsight-log-entry">' +
                        '<span class="avsight-log-timestamp">[' + timestamp + ']</span> ' +
                        '<span class="avsight-log-level-' + level + '">' + message + '</span>' +
                        '</div>';
                    $('#avsight-sync-log').append(logEntry);
                    $('#avsight-sync-log').scrollTop($('#avsight-sync-log')[0].scrollHeight);
                }

                // Simple status check for admin page (main logic is in AvsightSync.php)
                function updateSyncStatus() {
                    $.post(ajaxurl, {
                        action: 'avsight_get_sync_status',
                        nonce: nonce
                    }, function(response) {

                        if (response.sync_status) {
                            var status = response.sync_status;

                            // Update live log if available
                            if (status.log && status.log.length > 0) {
                                var logContainer = $('#avsight-sync-log');
                                if (logContainer.length > 0) {
                                    logContainer.empty();
                                    status.log.forEach(function(logEntry) {
                                        var timestamp = new Date(logEntry.timestamp).toLocaleTimeString();
                                        var logHtml = '<div class="avsight-log-entry">' +
                                            '<span class="avsight-log-timestamp">[' + timestamp + ']</span> ' +
                                            '<span class="avsight-log-level-info">' + logEntry.message + '</span>' +
                                            '</div>';
                                        logContainer.append(logHtml);
                                    });
                                    logContainer.scrollTop(logContainer[0].scrollHeight);
                                }
                            }
                        }

                        // Handle sync errors
                        if (response.error) {
                            isSyncRunning = false;
                        }
                    });
                }

                // Format time helper
                function formatTime(seconds) {
                    if (seconds === 'N/A') return 'N/A';
                    if (seconds < 0) seconds = 0;
                    var h = Math.floor(seconds / 3600);
                    var m = Math.floor((seconds % 3600) / 60);
                    var s = Math.floor(seconds % 60);
                    return [h, m, s]
                        .map(v => v < 10 ? "0" + v : v)
                        .filter((v, i) => v !== "00" || i > 0)
                        .join(":");
                }

                // Handle sync form submission
                $('form').on('submit', function(e) {
                    var form = $(this);
                    if (form.find('input[name="avsight_sync_nonce"]').length > 0) {
                        isSyncRunning = true;
                        setTimeout(function() {
                            addSyncLogEntry('info', 'Initiating inventory synchronization...');
                        }, 2000);
                    }
                });

                // Handle debug mode toggle
                $('input[name="debug_mode"]').on('change', function() {
                    if ($(this).is(':checked')) {
                        $('#sync-limit-row').show();
                    } else {
                        $('#sync-limit-row').hide();
                        $('input[name="sync_limit"]').val('0');
                    }
                });

                // Handle clear log buttons
                $('#avsight-sync-log-clear').on('click', function() {
                    $('#avsight-sync-log').empty();
                });

                $('#avsight-delete-log-clear').on('click', function() {
                    $('#avsight-delete-log').empty();
                });

                // Update status every 3 seconds (main logic is in AvsightSync.php)
                setInterval(updateSyncStatus, 3000);

                // Initial update
                updateSyncStatus();
            });
        </script>
<?php
    }

    /**
     * Render the Data Mappings tab
     */
    private static function renderMappingsTab()
    {
        echo '<div class="wrap">';
        echo '<h3>🔄 Data Mappings</h3>';
        echo '<p>Configure how Avsight data is mapped and processed for WooCommerce products.</p>';

        // Application Code Mappings Section
        echo '<div style="background: white; border: 1px solid #ccd0d4; border-radius: 4px; padding: 20px; margin-bottom: 20px;">';
        echo '<h4>🚁 Application Code Mappings</h4>';
        echo '<p>Group and map aircraft application codes for better product filtering.</p>';

        $app_mappings = get_option('avsight_application_code_mappings', []);

        echo '<form method="post" action="">';
        wp_nonce_field('avsight_save_app_mappings', 'avsight_save_app_mappings_nonce');

        echo '<table class="widefat" style="margin-bottom: 15px;">';
        echo '<thead><tr><th>Raw Application Code</th><th>Display Name</th><th>Action</th></tr></thead>';
        echo '<tbody id="app-mappings-table">';

        if (!empty($app_mappings)) {
            foreach ($app_mappings as $index => $mapping) {
                echo '<tr>';
                echo '<td><input type="text" name="app_mappings[' . $index . '][raw]" value="' . esc_attr($mapping['raw']) . '" placeholder="207s/206s" style="width: 100%;" /></td>';
                echo '<td><input type="text" name="app_mappings[' . $index . '][display]" value="' . esc_attr($mapping['display']) . '" placeholder="Bell 206/207 Series" style="width: 100%;" /></td>';
                echo '<td><button type="button" class="button remove-mapping">Remove</button></td>';
                echo '</tr>';
            }
        }

        echo '</tbody></table>';
        echo '<button type="button" id="add-app-mapping" class="button">Add Mapping</button>';
        echo '<p><input type="submit" name="save_app_mappings" class="button button-primary" value="Save Application Mappings"></p>';
        echo '</form>';
        echo '</div>';

        // Condition Code Mappings Section
        echo '<div style="background: white; border: 1px solid #ccd0d4; border-radius: 4px; padding: 20px; margin-bottom: 20px;">';
        echo '<h4>🔧 Condition Code Mappings</h4>';
        echo '<p>Map Avsight condition codes to display values for product filtering.</p>';

        $condition_mappings = get_option('avsight_condition_code_mappings', []);

        echo '<form method="post" action="">';
        wp_nonce_field('avsight_save_condition_mappings', 'avsight_save_condition_mappings_nonce');

        echo '<table class="widefat" style="margin-bottom: 15px;">';
        echo '<thead><tr><th>Avsight Condition Code</th><th>Display Value</th><th>Action</th></tr></thead>';
        echo '<tbody id="condition-mappings-table">';

        if (!empty($condition_mappings)) {
            foreach ($condition_mappings as $index => $mapping) {
                echo '<tr>';
                echo '<td><input type="text" name="condition_mappings[' . $index . '][raw]" value="' . esc_attr($mapping['raw']) . '" placeholder="INS&TEST" style="width: 100%;" /></td>';
                echo '<td><input type="text" name="condition_mappings[' . $index . '][display]" value="' . esc_attr($mapping['display']) . '" placeholder="IN" style="width: 100%;" /></td>';
                echo '<td><button type="button" class="button remove-mapping">Remove</button></td>';
                echo '</tr>';
            }
        }

        echo '</tbody></table>';
        echo '<button type="button" id="add-condition-mapping" class="button">Add Mapping</button>';
        echo '<p><input type="submit" name="save_condition_mappings" class="button button-primary" value="Save Condition Mappings"></p>';
        echo '</form>';
        echo '</div>';

        // Product Merging Rules Section
        echo '<div style="background: white; border: 1px solid #ccd0d4; border-radius: 4px; padding: 20px; margin-bottom: 20px;">';
        echo '<h4>🔀 Product Merging Rules</h4>';
        echo '<p>Configure which fields are used to merge duplicate products from Avsight.</p>';

        $merging_rules = get_option('avsight_product_merging_rules', [
            'part_number_field' => 'inscor__Product__r.Name',
            'warehouse_field' => 'inscor__Warehouse__r.Name',
            'condition_field' => 'inscor__Condition_Code__r.Name',
            'certification_field' => 'inscor__Comments__c',
            'serial_number_field' => 'inscor__Serial_Number__c',
            'location_field' => 'inscor__Location__r.Name'
        ]);

        echo '<form method="post" action="">';
        wp_nonce_field('avsight_save_merging_rules', 'avsight_save_merging_rules_nonce');

        echo '<table class="form-table">';
        echo '<tr><th>Part Number Field</th><td><input type="text" name="merging_rules[part_number_field]" value="' . esc_attr($merging_rules['part_number_field']) . '" style="width: 400px;" /></td></tr>';
        echo '<tr><th>Warehouse Field</th><td><input type="text" name="merging_rules[warehouse_field]" value="' . esc_attr($merging_rules['warehouse_field']) . '" style="width: 400px;" /></td></tr>';
        echo '<tr><th>Condition Field</th><td><input type="text" name="merging_rules[condition_field]" value="' . esc_attr($merging_rules['condition_field']) . '" style="width: 400px;" /></td></tr>';
        echo '<tr><th>Certification Field</th><td><input type="text" name="merging_rules[certification_field]" value="' . esc_attr($merging_rules['certification_field']) . '" style="width: 400px;" /></td></tr>';
        echo '<tr><th>Serial Number Field</th><td><input type="text" name="merging_rules[serial_number_field]" value="' . esc_attr($merging_rules['serial_number_field']) . '" style="width: 400px;" /></td></tr>';
        echo '<tr><th>Location Field</th><td><input type="text" name="merging_rules[location_field]" value="' . esc_attr($merging_rules['location_field']) . '" style="width: 400px;" /></td></tr>';
        echo '</table>';

        echo '<p><input type="submit" name="save_merging_rules" class="button button-primary" value="Save Merging Rules"></p>';
        echo '</form>';
        echo '</div>';

        echo '</div>';

        // Add JavaScript for dynamic table management
        echo '<script>
        jQuery(document).ready(function($) {
            // Add application mapping
            $("#add-app-mapping").click(function() {
                var index = $("#app-mappings-table tr").length;
                var row = "<tr>" +
                    "<td><input type=\"text\" name=\"app_mappings[" + index + "][raw]\" placeholder=\"207s/206s\" style=\"width: 100%;\" /></td>" +
                    "<td><input type=\"text\" name=\"app_mappings[" + index + "][display]\" placeholder=\"Bell 206/207 Series\" style=\"width: 100%;\" /></td>" +
                    "<td><button type=\"button\" class=\"button remove-mapping\">Remove</button></td>" +
                    "</tr>";
                $("#app-mappings-table").append(row);
            });

            // Add condition mapping
            $("#add-condition-mapping").click(function() {
                var index = $("#condition-mappings-table tr").length;
                var row = "<tr>" +
                    "<td><input type=\"text\" name=\"condition_mappings[" + index + "][raw]\" placeholder=\"INS&TEST\" style=\"width: 100%;\" /></td>" +
                    "<td><input type=\"text\" name=\"condition_mappings[" + index + "][display]\" placeholder=\"IN\" style=\"width: 100%;\" /></td>" +
                    "<td><button type=\"button\" class=\"button remove-mapping\">Remove</button></td>" +
                    "</tr>";
                $("#condition-mappings-table").append(row);
            });

            // Remove mapping
            $(document).on("click", ".remove-mapping", function() {
                $(this).closest("tr").remove();
            });
        });
        </script>';
    }

    /**
     * Save application code mappings
     */
    private static function saveApplicationCodeMappings()
    {
        $mappings = [];
        if (isset($_POST['app_mappings']) && is_array($_POST['app_mappings'])) {
            foreach ($_POST['app_mappings'] as $mapping) {
                if (!empty($mapping['raw']) && !empty($mapping['display'])) {
                    $mappings[] = [
                        'raw' => sanitize_text_field($mapping['raw']),
                        'display' => sanitize_text_field($mapping['display'])
                    ];
                }
            }
        }

        update_option('avsight_application_code_mappings', $mappings);
        echo '<div class="notice notice-success is-dismissible"><p><strong>Application code mappings saved successfully!</strong></p></div>';
    }

    /**
     * Save condition code mappings
     */
    private static function saveConditionCodeMappings()
    {
        $mappings = [];
        if (isset($_POST['condition_mappings']) && is_array($_POST['condition_mappings'])) {
            foreach ($_POST['condition_mappings'] as $mapping) {
                if (!empty($mapping['raw']) && !empty($mapping['display'])) {
                    $mappings[] = [
                        'raw' => sanitize_text_field($mapping['raw']),
                        'display' => sanitize_text_field($mapping['display'])
                    ];
                }
            }
        }

        update_option('avsight_condition_code_mappings', $mappings);
        echo '<div class="notice notice-success is-dismissible"><p><strong>Condition code mappings saved successfully!</strong></p></div>';
    }

    /**
     * Save product merging rules
     */
    private static function saveProductMergingRules()
    {
        $rules = [];
        if (isset($_POST['merging_rules']) && is_array($_POST['merging_rules'])) {
            $rules = [
                'part_number_field' => sanitize_text_field($_POST['merging_rules']['part_number_field']),
                'warehouse_field' => sanitize_text_field($_POST['merging_rules']['warehouse_field']),
                'condition_field' => sanitize_text_field($_POST['merging_rules']['condition_field']),
                'certification_field' => sanitize_text_field($_POST['merging_rules']['certification_field']),
                'serial_number_field' => sanitize_text_field($_POST['merging_rules']['serial_number_field']),
                'location_field' => sanitize_text_field($_POST['merging_rules']['location_field'])
            ];
        }

        update_option('avsight_product_merging_rules', $rules);
        echo '<div class="notice notice-success is-dismissible"><p><strong>Product merging rules saved successfully!</strong></p></div>';
    }

    /**
     * Apply application code mapping to a raw value
     *
     * @param string $raw_value Raw application code from Avsight
     * @return string Mapped display value or original if no mapping found
     */
    public static function applyApplicationCodeMapping($raw_value)
    {
        if (empty($raw_value)) {
            return $raw_value;
        }

        $mappings = get_option('avsight_application_code_mappings', []);

        foreach ($mappings as $mapping) {
            if ($mapping['raw'] === $raw_value) {
                return $mapping['display'];
            }
        }

        return $raw_value; // Return original if no mapping found
    }

    /**
     * Apply condition code mapping to a raw value
     *
     * @param string $raw_value Raw condition code from Avsight
     * @return string Mapped display value or original if no mapping found
     */
    public static function applyConditionCodeMapping($raw_value)
    {
        if (empty($raw_value)) {
            return $raw_value;
        }

        $mappings = get_option('avsight_condition_code_mappings', []);

        foreach ($mappings as $mapping) {
            if ($mapping['raw'] === $raw_value) {
                return $mapping['display'];
            }
        }

        return $raw_value; // Return original if no mapping found
    }

    /**
     * Get product merging rules
     *
     * @return array Merging rules configuration
     */
    public static function getProductMergingRules()
    {
        return get_option('avsight_product_merging_rules', [
            'part_number_field' => 'inscor__Product__r.Name',
            'warehouse_field' => 'inscor__Warehouse__r.Name',
            'condition_field' => 'inscor__Condition_Code__r.Name',
            'certification_field' => 'inscor__Comments__c',
            'serial_number_field' => 'inscor__Serial_Number__c',
            'location_field' => 'inscor__Location__r.Name'
        ]);
    }
}
