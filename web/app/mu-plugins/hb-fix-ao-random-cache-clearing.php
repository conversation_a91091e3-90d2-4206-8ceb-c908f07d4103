<?php

/**
 * Plugin Name: [Hummingbird Pro] - Fix Asset Optimization clearing the Page Cache randomly (Rev. 3)
 * Description: Prevents the Cache Clearing process to be triggered after an AO queue completed under specific circumstances
 * Author: <PERSON> @ WPMUDEV
 * Task: SLS-4239 / SLS-3756 / SLS-3786
 * Author URI: https://premium.wpmudev.org
 * License: GPLv2 or later
 */

add_action('plugins_loaded', function () {
	if (! class_exists('\Hummingbird\WP_Hummingbird')) {
		return;
	}

	if (! class_exists('WPMUDEV_Hummingbird_Custom_Clear_Cache')) {
		class WPMUDEV_Hummingbird_Custom_Clear_Cache
		{
			private static $instance;
			private $lock = false;
			private $hbmodules;

			public static function get_instance()
			{
				if (null === self::$instance) {
					self::$instance = new self();
				}
				return self::$instance;
			}

			public function __construct()
			{
				$hummingbird_instance = \Hummingbird\WP_Hummingbird::get_instance();
				if ($hummingbird_instance && isset($hummingbird_instance->core) && isset($hummingbird_instance->core->modules)) {
					$this->hbmodules = $hummingbird_instance->core->modules;
					$this->replace_hooks();
				}
			}

			private function replace_hooks()
			{
				global $wp_filter;

				$callbacks = array(
					'wphb_clear_page_cache' => array(
						'clear_cache_action',
						'\Hummingbird\Core\Modules\Page_Cache'
					),
				);

				foreach ($callbacks as $tag => list($hook_method, $hook_class)) {
					if (! isset($wp_filter[$tag])) {
						return;
					}
					foreach ($wp_filter[$tag]->callbacks as $key => $callback_array) {
						foreach ($callback_array as $c_key => $callback) {
							if (substr_compare($c_key, $hook_method, strlen($c_key) - strlen($hook_method), strlen($hook_method)) === 0) {
								if ($callback['function'][0] instanceof $hook_class) {
									unset($wp_filter[$tag]->callbacks[$key][$c_key]);
								}
							}
						}
					}
				}

				add_action('wphb_minify_process_queue', array($this, 'pre_cron_process_queue'), 9);
				add_action('wp_footer', array($this, 'lock_ao_queue'), 9999);
				add_action('wphb_clear_page_cache', array($this, 'clear_cache_action'));
				add_action('wp_footer', array($this, 'unlock_ao_queue'), 10001);
			}

			public function pre_cron_process_queue()
			{
				if (! isset($this->hbmodules['minify'])) {
					return;
				}

				$hb_minify   = $this->hbmodules['minify'];
				$queue_count = count($hb_minify->get_pending_persistent_queue());

				if ($queue_count <= 9) {
					$this->lock_ao_queue();
				}
			}

			public function lock_ao_queue()
			{
				$this->lock = true;
			}

			public function unlock_ao_queue()
			{
				$this->lock = false;
			}

			public function clear_cache_action($post_id = false)
			{
				if (! isset($this->hbmodules['page_cache'])) {
					return;
				}

				$hb_cache = $this->hbmodules['page_cache'];

				if ($this->lock) {
					return;
				}

				call_user_func_array(array($hb_cache, 'clear_cache_action'), array($post_id));
				$this->lock = false;
			}
		}
		WPMUDEV_Hummingbird_Custom_Clear_Cache::get_instance();
	}
});
